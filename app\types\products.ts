/**
 * Unified Product Management System Types
 * Using variants-based approach instead of PWP/PWOP
 */

// Core Product Interface
export interface Product {
  id: string
  name: string
  slug: string
  description: string
  main_image: string
  category_id: string
  status: 'active' | 'inactive'
  has_variants: boolean
  variants: ProductVariant[]
  custom_fields: CustomField[]
  rating?: number
  comment_count?: number
  featured?: boolean
  tags?: string[]
  created_at: string
  updated_at: string
}

// Product Variant Interface
export interface ProductVariant {
  id: string
  product_id: string
  name: string
  image?: string
  original_price: number
  user_price: number
  discount_price?: number
  distributor_price?: number
  inventory_type: 'manual' | 'digital_codes'
  manual_quantity?: number
  available_codes?: number
  is_default: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

// Custom Field Interface
export interface CustomField {
  id: string
  product_id: string
  field_name: string
  field_description: string
  field_type: 'text' | 'dropdown'
  dropdown_options?: string[]
  sort_order: number
  created_at: string
  updated_at: string
}

// Digital Code Interface
export interface DigitalCode {
  id: string
  variant_id: string
  code: string
  status: 'available' | 'used' | 'reserved'
  order_id?: string
  used_at?: string
  created_at: string
  updated_at: string
}

// Category Interface
export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  image: string
  is_active: boolean
  product_count?: number
  created_at: string
  updated_at: string
}

// Order Interface
export interface Order {
  id: string
  user_id: string
  product_id: string
  variant_id: string
  amount_paid: number
  currency: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  custom_field_values?: Record<string, any>
  delivery_code?: string
  assigned_worker?: string
  notes?: string
  created_at: string
  updated_at: string
  completed_at?: string
}

// Order Code Assignment Interface
export interface OrderCode {
  id: string
  order_id: string
  digital_code_id: string
  assigned_at: string
}

// API Response Types
export interface ProductsResponse {
  products: Product[]
  total: number
  page: number
  limit: number
  total_pages: number
}

export interface ProductResponse {
  product: Product
}

// Filter Types
export interface ProductFilters {
  category?: string
  search?: string
  min_price?: number
  max_price?: number
  status?: 'active' | 'inactive'
  has_variants?: boolean
  sort_by?: 'name' | 'price' | 'created_at'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// Form Types for Admin
export interface ProductFormData {
  name: string
  slug: string
  description: string
  main_image: string
  category_id: string
  status: 'active' | 'inactive'
  has_variants: boolean
  featured?: boolean
  tags?: string[]
}

export interface VariantFormData {
  name: string
  image?: string
  original_price: number
  user_price: number
  discount_price?: number
  distributor_price?: number
  inventory_type: 'manual' | 'digital_codes'
  manual_quantity?: number
  is_default: boolean
}

export interface CustomFieldFormData {
  field_name: string
  field_description: string
  field_type: 'text' | 'dropdown'
  dropdown_options?: string[]
}

// Validation Types
export interface ValidationError {
  field: string
  message: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

// Statistics Types
export interface ProductStats {
  total_products: number
  active_products: number
  inactive_products: number
  products_with_variants: number
  total_variants: number
  digital_products: number
  average_price: number
}

// Utility Types
export type ProductStatus = Product['status']
export type VariantInventoryType = ProductVariant['inventory_type']
export type CustomFieldType = CustomField['field_type']
export type OrderStatus = Order['status']
export type DigitalCodeStatus = DigitalCode['status']
