<template>
  <div class="space-y-4 sm:space-y-6" dir="rtl">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold text-white-force">إدارة المنتجات</h1>
        <p class="text-sm sm:text-base text-theme-text-muted">إدارة المنتجات والأسعار والمخزون</p>
      </div>

      <div class="flex items-center space-x-2 sm:space-x-3 space-x-reverse">
        <button
          @click="addProduct"
          class="btn-primary px-3 py-2 sm:px-4 sm:py-2 text-sm sm:text-base"
        >
          <svg class="w-4 h-4 ml-1 sm:ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          إضافة منتج
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-5 xl:gap-6">
      <AdminStatCard
        title="إجمالي المنتجات"
        :value="stats.totalProducts"
        icon="orders"
        icon-color="blue"
        :loading="loading"
      />

      <AdminStatCard
        title="المنتجات النشطة"
        :value="stats.activeProducts"
        icon="orders"
        icon-color="green"
        :loading="loading"
      />

      <AdminStatCard
        title="منتجات متعددة المتغيرات"
        :value="stats.productsWithVariants"
        icon="orders"
        icon-color="purple"
        :loading="loading"
      />

      <AdminStatCard
        title="متوسط السعر"
        :value="stats.averagePrice"
        unit="USD"
        icon="revenue"
        icon-color="yellow"
        :loading="loading"
      />
    </div>

    <!-- Products Table -->
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl sm:rounded-3xl p-4 sm:p-6 shadow-xl">
      <h3 class="text-lg sm:text-xl font-semibold text-white-force mb-4 sm:mb-6">قائمة المنتجات</h3>

      <!-- Filters -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
        <!-- Search -->
        <div>
          <input
            v-model="filters.search"
            type="text"
            placeholder="البحث بالاسم أو الوصف"
            class="w-full px-3 py-2 text-sm sm:text-base bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary transition-all duration-200"
          />
        </div>

        <!-- Variants Filter -->
        <div>
          <select
            v-model="filters.has_variants"
            class="w-full px-3 py-2 text-sm sm:text-base bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary transition-all duration-200"
          >
            <option value="">جميع الأنواع</option>
            <option value="false">منتج بسيط</option>
            <option value="true">منتج متعدد المتغيرات</option>
          </select>
        </div>

        <!-- Status Filter -->
        <div>
          <select
            v-model="filters.status"
            class="w-full px-3 py-2 text-sm sm:text-base bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary transition-all duration-200"
          >
            <option value="">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>
        </div>

        <!-- Clear Filters -->
        <div>
          <button
            @click="clearFilters"
            class="w-full btn-secondary py-2 text-sm sm:text-base"
          >
            مسح الفلاتر
          </button>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-5 xl:gap-6">
        <div 
          v-for="product in filteredProducts" 
          :key="product.id"
          class="bg-theme-surface/30 rounded-2xl p-4 hover:bg-theme-surface/50 transition-colors"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-12 h-12 rounded-lg overflow-hidden bg-theme-surface">
                <img v-if="product.image" :src="product.image" :alt="product.name" class="w-full h-full object-cover" />
                <div v-else class="w-full h-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-theme-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                  </svg>
                </div>
              </div>
              <div>
                <h4 class="font-medium text-white-force">{{ product.name }}</h4>
                <p class="text-sm text-theme-text-muted">{{ product.category }}</p>
              </div>
            </div>
            <span 
              class="px-2 py-1 rounded-full text-xs font-medium"
              :class="getStatusClass(product.status)"
            >
              {{ getStatusText(product.status) }}
            </span>
          </div>
          
          <div class="grid grid-cols-2 gap-4 text-sm mb-4">
            <div>
              <span class="text-theme-text-muted">النوع:</span>
              <span
                class="text-xs px-2 py-1 rounded-full mr-2"
                :class="getVariantTypeClass(product.has_variants)"
              >
                {{ getVariantTypeText(product.has_variants, product.variants_count) }}
              </span>
            </div>
            <div>
              <span class="text-theme-text-muted">السعر:</span>
              <span class="text-white-force font-medium mr-2">{{ formatPriceRange(product.min_price, product.max_price) }}</span>
            </div>
            <div>
              <span class="text-theme-text-muted">المخزون:</span>
              <span class="text-white-force font-medium mr-2">{{ product.total_stock }}</span>
            </div>
            <div>
              <span class="text-theme-text-muted">المبيعات:</span>
              <span class="text-white-force font-medium mr-2">{{ product.sales_count || 0 }}</span>
            </div>
          </div>
          
          <div class="flex justify-end space-x-2 space-x-reverse">
            <button
              @click="viewProduct(product)"
              class="btn-secondary text-xs px-3 py-1"
            >
              عرض
            </button>
            <button
              @click="editProduct(product)"
              class="btn-primary text-xs px-3 py-1"
            >
              تعديل
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Modal -->
    <AdminProductModal
      :is-open="showProductModal"
      :product="editingProduct"
      @close="showProductModal = false"
      @save="handleProductSave"
    />
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'admin'
})

// Import mock products data with variants
import { mockProducts, mockCategories } from '~/data/mockProductsData'

// Convert mock data to admin format
const products = ref(mockProducts.map(product => ({
  id: product.id,
  name: product.name,
  slug: product.slug,
  category: mockCategories.find(cat => cat.id === product.category_id)?.name || 'غير محدد',
  category_id: product.category_id,
  status: product.status,
  has_variants: product.has_variants,
  variants_count: product.variants.length,
  min_price: Math.min(...product.variants.map(v => v.user_price)),
  max_price: Math.max(...product.variants.map(v => v.user_price)),
  total_stock: product.variants.reduce((sum, v) => {
    if (v.inventory_type === 'manual') return sum + (v.manual_quantity || 0)
    if (v.inventory_type === 'digital_codes') return sum + (v.available_codes || 0)
    return sum
  }, 0),
  sales_count: product.comment_count || 0,
  image: product.main_image,
  featured: product.featured || false,
  created_at: product.created_at,
  updated_at: product.updated_at
})))

const loading = ref(false)

// Filters
const filters = ref({
  search: '',
  status: '',
  has_variants: ''
})

// Computed
const filteredProducts = computed(() => {
  let result = products.value

  if (filters.value.search) {
    const search = filters.value.search.toLowerCase()
    result = result.filter(product => 
      product.name.toLowerCase().includes(search) ||
      product.category.toLowerCase().includes(search)
    )
  }

  if (filters.value.has_variants) {
    const hasVariants = filters.value.has_variants === 'true'
    result = result.filter(product => product.has_variants === hasVariants)
  }

  if (filters.value.status) {
    result = result.filter(product => product.status === filters.value.status)
  }

  return result
})

const stats = computed(() => ({
  totalProducts: products.value.length,
  activeProducts: products.value.filter(p => p.status === 'active').length,
  inactiveProducts: products.value.filter(p => p.status === 'inactive').length,
  productsWithVariants: products.value.filter(p => p.has_variants).length,
  averagePrice: Math.round(products.value.reduce((sum, p) => sum + p.min_price, 0) / products.value.length)
}))

// Modal state
const showProductModal = ref(false)
const editingProduct = ref<any>(null)

// Methods
const viewProduct = (product: any) => {
  // Navigate to product detail page
  navigateTo(`/product/${product.slug}`)
}

const editProduct = (product: any) => {
  editingProduct.value = product
  showProductModal.value = true
}

const addProduct = () => {
  editingProduct.value = null
  showProductModal.value = true
}

const handleProductSave = (productData: any) => {
  // TODO: Implement actual save logic
  console.log('Saving product:', productData)

  if (editingProduct.value) {
    // Update existing product
    const index = products.value.findIndex(p => p.id === editingProduct.value.id)
    if (index !== -1) {
      products.value[index] = { ...products.value[index], ...productData }
    }
  } else {
    // Add new product
    const newProduct = {
      id: `prod_${Date.now()}`,
      ...productData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    products.value.push(newProduct)
  }

  showProductModal.value = false
  editingProduct.value = null
}

const toggleProductStatus = (product: any) => {
  product.status = product.status === 'active' ? 'inactive' : 'active'
}

const clearFilters = () => {
  filters.value = {
    search: '',
    status: '',
    has_variants: ''
  }
}

// Helper functions
const getVariantTypeClass = (hasVariants: boolean) => {
  return hasVariants
    ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
    : 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
}

const getVariantTypeText = (hasVariants: boolean, variantsCount: number) => {
  return hasVariants ? `متعدد (${variantsCount})` : 'بسيط'
}

const formatPriceRange = (minPrice: number, maxPrice: number) => {
  if (minPrice === maxPrice) {
    return `$${minPrice.toFixed(2)}`
  }
  return `$${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`
}



const getStatusClass = (status: string) => {
  const classes = {
    active: 'bg-green-500/20 text-green-400 border border-green-500/30',
    inactive: 'bg-red-500/20 text-red-400 border border-red-500/30'
  }
  return classes[status as keyof typeof classes] || classes.active
}

const getStatusText = (status: string) => {
  const texts = {
    active: 'نشط',
    inactive: 'غير نشط'
  }
  return texts[status as keyof typeof texts] || status
}
</script>
