export const useAdminOrders = () => {
  const orders = ref([
    {
      id: 'ORD-001',
      productName: 'بطاقة ستيم 50 دولار',
      productType: 'code',
      userName: 'أحمد محمد',
      userEmail: '<EMAIL>',
      paidCurrency: 'SAR',
      paidAmount: 187.50,
      exchangeRate: 3.75,
      usdEquivalent: 50.00,
      status: 'completed',
      assignedWorker: 'فاطمة علي',
      createdAt: '2024-07-30T10:30:00Z',
      completedAt: '2024-07-30T11:15:00Z',
      notes: 'تم التسليم بنجاح',
      deliveryCode: 'STEAM-XXXX-XXXX-XXXX'
    },
    {
      id: 'ORD-002',
      productName: 'حساب نتفليكس مشترك',
      productType: 'package',
      userName: 'فاطمة علي',
      userEmail: '<EMAIL>',
      paidCurrency: 'USD',
      paidAmount: 15.99,
      exchangeRate: 1.0,
      usdEquivalent: 15.99,
      status: 'pending',
      assignedWorker: null,
      createdAt: '2024-07-30T14:20:00Z',
      completedAt: null,
      notes: 'في انتظار التعيين',
      deliveryCode: null
    },
    {
      id: 'ORD-003',
      productName: 'بطاقة جوجل بلاي 25 دولار',
      productType: 'code',
      userName: 'محمد حسن',
      userEmail: '<EMAIL>',
      paidCurrency: 'AED',
      paidAmount: 91.75,
      exchangeRate: 3.67,
      usdEquivalent: 25.00,
      status: 'processing',
      assignedWorker: 'فاطمة علي',
      createdAt: '2024-07-30T09:45:00Z',
      completedAt: null,
      notes: 'جاري المعالجة',
      deliveryCode: null
    },
    {
      id: 'ORD-004',
      productName: 'اشتراك سبوتيفاي بريميوم',
      productType: 'simple',
      userName: 'خالد عبدالله',
      userEmail: '<EMAIL>',
      paidCurrency: 'SAR',
      paidAmount: 37.50,
      exchangeRate: 3.75,
      usdEquivalent: 10.00,
      status: 'declined',
      assignedWorker: null,
      createdAt: '2024-07-29T16:30:00Z',
      completedAt: null,
      notes: 'رفض بسبب عدم توفر المنتج',
      deliveryCode: null
    },
    {
      id: 'ORD-005',
      productName: 'بطاقة أمازون 100 دولار',
      productType: 'code',
      userName: 'عائشة أحمد',
      userEmail: '<EMAIL>',
      paidCurrency: 'USD',
      paidAmount: 100.00,
      exchangeRate: 1.0,
      usdEquivalent: 100.00,
      status: 'completed',
      assignedWorker: 'فاطمة علي',
      createdAt: '2024-07-29T11:20:00Z',
      completedAt: '2024-07-29T12:45:00Z',
      notes: 'تم التسليم بنجاح',
      deliveryCode: 'AMZN-XXXX-XXXX-XXXX'
    }
  ])

  const workers = ref([
    { id: 2, name: 'فاطمة علي', email: '<EMAIL>', activeOrders: 2 },
    { id: 4, name: 'عائشة أحمد', email: '<EMAIL>', activeOrders: 0 }
  ])

  const loading = ref(false)
  const error = ref<string | null>(null)

  // Filters
  const filters = ref({
    status: '',
    dateFrom: '',
    dateTo: '',
    search: ''
  })

  // Computed filtered orders
  const filteredOrders = computed(() => {
    let result = orders.value

    if (filters.value.status) {
      result = result.filter(order => order.status === filters.value.status)
    }

    if (filters.value.dateFrom) {
      result = result.filter(order => 
        new Date(order.createdAt) >= new Date(filters.value.dateFrom)
      )
    }

    if (filters.value.dateTo) {
      result = result.filter(order => 
        new Date(order.createdAt) <= new Date(filters.value.dateTo)
      )
    }

    if (filters.value.search) {
      const search = filters.value.search.toLowerCase()
      result = result.filter(order => 
        order.id.toLowerCase().includes(search) ||
        order.productName.toLowerCase().includes(search) ||
        order.userName.toLowerCase().includes(search)
      )
    }

    return result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  })

  // Stats
  const stats = computed(() => {
    const totalOrders = orders.value.length
    const completedOrders = orders.value.filter(o => o.status === 'completed').length
    const pendingOrders = orders.value.filter(o => o.status === 'pending').length
    const processingOrders = orders.value.filter(o => o.status === 'processing').length
    const totalRevenue = orders.value
      .filter(o => o.status === 'completed')
      .reduce((sum, order) => sum + order.usdEquivalent, 0)

    return {
      totalOrders,
      completedOrders,
      pendingOrders,
      processingOrders,
      totalRevenue,
      completionRate: totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0
    }
  })

  // Methods
  const getOrderById = (id: string) => {
    return orders.value.find(order => order.id === id)
  }

  const updateOrderStatus = async (orderId: string, newStatus: string, notes?: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const order = orders.value.find(o => o.id === orderId)
      if (order) {
        order.status = newStatus
        if (notes) {
          order.notes = notes
        }
        if (newStatus === 'completed') {
          order.completedAt = new Date().toISOString()
        }
      }
    } catch (err) {
      error.value = 'فشل في تحديث حالة الطلب'
      throw err
    } finally {
      loading.value = false
    }
  }

  const assignWorker = async (orderId: string, workerName: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const order = orders.value.find(o => o.id === orderId)
      if (order) {
        order.assignedWorker = workerName
        if (order.status === 'pending') {
          order.status = 'processing'
        }
      }
    } catch (err) {
      error.value = 'فشل في تعيين العامل'
      throw err
    } finally {
      loading.value = false
    }
  }

  const addDeliveryCode = async (orderId: string, code: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const order = orders.value.find(o => o.id === orderId)
      if (order) {
        order.deliveryCode = code
        order.status = 'completed'
        order.completedAt = new Date().toISOString()
      }
    } catch (err) {
      error.value = 'فشل في إضافة كود التسليم'
      throw err
    } finally {
      loading.value = false
    }
  }

  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    Object.assign(filters.value, newFilters)
  }

  const clearFilters = () => {
    filters.value = {
      status: '',
      dateFrom: '',
      dateTo: '',
      search: ''
    }
  }

  // Revenue by time period
  const getRevenueByPeriod = (period: 'day' | 'week' | 'month') => {
    const now = new Date()
    const completedOrders = orders.value.filter(o => o.status === 'completed')
    
    return completedOrders
      .filter(order => {
        const orderDate = new Date(order.createdAt)
        const diffTime = now.getTime() - orderDate.getTime()
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        
        switch (period) {
          case 'day': return diffDays <= 1
          case 'week': return diffDays <= 7
          case 'month': return diffDays <= 30
          default: return true
        }
      })
      .reduce((sum, order) => sum + order.usdEquivalent, 0)
  }

  return {
    orders: readonly(orders),
    workers: readonly(workers),
    filteredOrders,
    stats,
    loading: readonly(loading),
    error: readonly(error),
    filters: readonly(filters),
    getOrderById,
    updateOrderStatus,
    assignWorker,
    addDeliveryCode,
    setFilters,
    clearFilters,
    getRevenueByPeriod
  }
}
