<template>
  <div class="min-h-screen bg-theme-background" dir="rtl">
    <!-- Promotional Banner -->
    <section class="container mx-auto px-4 pt-2 pb-6">
      <PromoBanner />
    </section>

    <!-- Development Admin Login (Remove in production) -->
    <section v-if="!isAuthenticated" class="container mx-auto px-4 pb-6">
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-4">
        <h3 class="text-lg font-semibold text-white-force mb-3">تسجيل دخول سريع للتطوير</h3>
        <div class="flex flex-wrap gap-2">
          <button @click="quickLogin('admin')" class="btn-primary text-sm px-4 py-2">
            دخول كمدير
          </button>
          <button @click="quickLogin('worker')" class="btn-secondary text-sm px-4 py-2">
            دخول كعامل
          </button>
          <button @click="quickLogin('user')" class="btn-secondary text-sm px-4 py-2">
            دخول كمستخدم
          </button>
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section class="container mx-auto px-4 pb-8">
      <GridView
        :items="categories"
        type="categories"
        title="تصفح الفئات"
        :show-view-all="false"
      />
    </section>

    <!-- Featured Products Section -->
    <section class="container mx-auto px-4 pb-8">
      <GridView
        :items="featuredProducts"
        type="products"
        title="المنتجات المميزة"
        :limit="6"
        view-all-link="/shop?filter=featured"
      />
    </section>


  </div>
</template>

<script setup lang="ts">
import { mockProducts, mockCategories } from '~/data/mockProductsData'

// Auth store
const { isAuthenticated, mockLogin } = useAuthStore()

// SEO Meta
useSeoMeta({
  title: 'بنتاكون - متجر الألعاب الرقمية',
  description: 'أفضل متجر للألعاب الرقمية وبطاقات الهدايا في الشرق الأوسط',
  ogTitle: 'بنتاكون - متجر الألعاب الرقمية',
  ogDescription: 'أفضل متجر للألعاب الرقمية وبطاقات الهدايا في الشرق الأوسط',
  ogImage: '/og-image.jpg',
  twitterCard: 'summary_large_image',
})

// Data
const categories = mockCategories
const featuredProducts = mockProducts.filter(product => product.featured)

// Quick login for development
const quickLogin = async (role: 'admin' | 'worker' | 'user') => {
  try {
    await mockLogin(role)
    if (role === 'admin') {
      await navigateTo('/admin')
    }
  } catch (error) {
    console.error('Quick login failed:', error)
  }
}
</script>
