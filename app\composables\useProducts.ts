import type { 
  Product, 
  ProductVariant, 
  ProductFilters, 
  ValidationResult,
  ValidationError,
  ProductFormData,
  VariantFormData
} from '~/types/products'

export const useProducts = () => {
  const store = useProductsStore()

  // Computed properties
  const products = computed(() => store.products)
  const filteredProducts = computed(() => store.filteredProducts)
  const paginatedProducts = computed(() => store.paginatedProducts)
  const currentProduct = computed(() => store.currentProduct)
  const categories = computed(() => store.activeCategories)
  const loading = computed(() => store.loading)
  const error = computed(() => store.error)
  const filters = computed(() => store.filters)
  const totalPages = computed(() => store.totalPages)
  const stats = computed(() => store.stats)

  // Product operations
  const fetchProducts = (filters?: ProductFilters) => store.fetchProducts(filters)
  const fetchProduct = (id: string) => store.fetchProduct(id)
  const fetchCategories = () => store.fetchCategories()
  const setFilters = (filters: Partial<ProductFilters>) => store.setFilters(filters)
  const clearFilters = () => store.clearFilters()
  const clearError = () => store.clearError()

  // Product utilities
  const getProductBySlug = (slug: string) => store.getProductBySlug(slug)
  const getProductById = (id: string) => store.getProductById(id)
  const getCheapestVariant = (productId: string) => store.getCheapestVariant(productId)
  const getProductDisplayPrice = (productId: string) => store.getProductDisplayPrice(productId)

  // Validation functions
  const validateProduct = (productData: ProductFormData): ValidationResult => {
    const errors: ValidationError[] = []

    if (!productData.name?.trim()) {
      errors.push({ field: 'name', message: 'اسم المنتج مطلوب' })
    }

    if (!productData.slug?.trim()) {
      errors.push({ field: 'slug', message: 'رابط المنتج مطلوب' })
    } else if (!/^[a-z0-9-]+$/.test(productData.slug)) {
      errors.push({ field: 'slug', message: 'رابط المنتج يجب أن يحتوي على أحرف إنجليزية صغيرة وأرقام وشرطات فقط' })
    }

    if (!productData.description?.trim()) {
      errors.push({ field: 'description', message: 'وصف المنتج مطلوب' })
    }

    if (!productData.main_image?.trim()) {
      errors.push({ field: 'main_image', message: 'صورة المنتج مطلوبة' })
    }

    if (!productData.category_id?.trim()) {
      errors.push({ field: 'category_id', message: 'فئة المنتج مطلوبة' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  const validateVariant = (variantData: VariantFormData): ValidationResult => {
    const errors: ValidationError[] = []

    if (!variantData.name?.trim()) {
      errors.push({ field: 'name', message: 'اسم المتغير مطلوب' })
    }

    if (!variantData.original_price || variantData.original_price <= 0) {
      errors.push({ field: 'original_price', message: 'السعر الأصلي يجب أن يكون أكبر من صفر' })
    }

    if (!variantData.user_price || variantData.user_price <= 0) {
      errors.push({ field: 'user_price', message: 'سعر المستخدم يجب أن يكون أكبر من صفر' })
    }

    if (variantData.original_price && variantData.user_price && variantData.user_price <= variantData.original_price) {
      errors.push({ field: 'user_price', message: 'سعر المستخدم يجب أن يكون أكبر من السعر الأصلي' })
    }

    if (variantData.distributor_price && variantData.user_price && variantData.distributor_price >= variantData.user_price) {
      errors.push({ field: 'distributor_price', message: 'سعر الموزع يجب أن يكون أقل من سعر المستخدم' })
    }

    if (variantData.discount_price && variantData.user_price && variantData.discount_price >= variantData.user_price) {
      errors.push({ field: 'discount_price', message: 'السعر المخفض يجب أن يكون أقل من سعر المستخدم' })
    }

    if (variantData.inventory_type === 'manual' && (!variantData.manual_quantity || variantData.manual_quantity < 0)) {
      errors.push({ field: 'manual_quantity', message: 'الكمية يجب أن تكون صفر أو أكبر' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Pricing utilities
  const calculateDiscount = (originalPrice: number, discountPrice: number): number => {
    if (!originalPrice || !discountPrice || discountPrice >= originalPrice) return 0
    return Math.round(((originalPrice - discountPrice) / originalPrice) * 100)
  }

  const formatPrice = (price: number, currency = 'USD'): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(price)
  }

  // Inventory utilities
  const getAvailableQuantity = (variant: ProductVariant): number => {
    if (variant.inventory_type === 'digital_codes') {
      return variant.available_codes || 0
    } else {
      return variant.manual_quantity || 0
    }
  }

  const isInStock = (variant: ProductVariant): boolean => {
    return getAvailableQuantity(variant) > 0
  }

  const getStockStatus = (variant: ProductVariant): 'in_stock' | 'low_stock' | 'out_of_stock' => {
    const quantity = getAvailableQuantity(variant)
    
    if (quantity === 0) return 'out_of_stock'
    if (quantity <= 5) return 'low_stock'
    return 'in_stock'
  }

  // Product display utilities
  const shouldShowVariantSelector = (product: Product): boolean => {
    return product.has_variants && product.variants.length > 1
  }

  const getProductBadges = (product: Product) => {
    const badges = []

    if (product.featured) badges.push({ type: 'featured', text: 'مميز' })

    // Check for discounts
    const hasDiscount = product.variants.some(v => v.discount_price && v.discount_price < v.user_price)
    if (hasDiscount) {
      const maxDiscount = Math.max(...product.variants
        .filter(v => v.discount_price && v.discount_price < v.user_price)
        .map(v => calculateDiscount(v.user_price, v.discount_price!))
      )
      badges.push({ type: 'discount', text: `-${maxDiscount}%` })
    }

    return badges
  }

  // Search and filtering utilities
  const searchProducts = (query: string) => {
    setFilters({ search: query, page: 1 })
  }

  const filterByCategory = (categoryId: string) => {
    setFilters({ category: categoryId, page: 1 })
  }

  const filterByPriceRange = (minPrice?: number, maxPrice?: number) => {
    setFilters({ min_price: minPrice, max_price: maxPrice, page: 1 })
  }

  const sortProducts = (sortBy: string, sortOrder: 'asc' | 'desc' = 'desc') => {
    setFilters({ sort_by: sortBy as any, sort_order: sortOrder, page: 1 })
  }

  // Pagination utilities
  const goToPage = (page: number) => {
    setFilters({ page })
  }

  const nextPage = () => {
    if (filters.value.page! < totalPages.value) {
      goToPage(filters.value.page! + 1)
    }
  }

  const prevPage = () => {
    if (filters.value.page! > 1) {
      goToPage(filters.value.page! - 1)
    }
  }

  return {
    // State
    products,
    filteredProducts,
    paginatedProducts,
    currentProduct,
    categories,
    loading,
    error,
    filters,
    totalPages,
    stats,

    // Actions
    fetchProducts,
    fetchProduct,
    fetchCategories,
    setFilters,
    clearFilters,
    clearError,

    // Utilities
    getProductBySlug,
    getProductById,
    getCheapestVariant,
    getProductDisplayPrice,
    validateProduct,
    validateVariant,
    calculateDiscount,
    formatPrice,
    getAvailableQuantity,
    isInStock,
    getStockStatus,
    shouldShowVariantSelector,
    getProductBadges,

    // Search & Filter
    searchProducts,
    filterByCategory,
    filterByPriceRange,
    sortProducts,

    // Pagination
    goToPage,
    nextPage,
    prevPage
  }
}
