/**
 * Mock data for the new variants-based product system
 * This replaces the old PWP/PWOP approach
 */

import type { Product, Category, DigitalCode } from '~/types/products'

// Mock Categories
export const mockCategories: Category[] = [
  {
    id: "cat_1",
    name: "آر بي جي",
    slug: "rpg",
    description: "ألعاب تقمص الأدوار",
    image: "https://shop.ldrescdn.com/rms/ld-space/process/img/5ba76fd6bcce49d5a3f9e97c2c64b1691737079512.webp",
    is_active: true,
    product_count: 3,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  {
    id: "cat_2",
    name: "بطاقات هدايا",
    slug: "gift-cards",
    description: "بطاقات الهدايا الرقمية",
    image: "https://shop.ldrescdn.com/rms/ld-space/process/img/steam_gift_card_cover_art.webp",
    is_active: true,
    product_count: 2,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  {
    id: "cat_3",
    name: "Battle Royale",
    slug: "battle-royale",
    description: "ألعاب المعركة الملكية",
    image: "https://shop.ldrescdn.com/rms/ld-space/process/img/pubg_mobile_cover_art.webp",
    is_active: true,
    product_count: 1,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  {
    id: "cat_4",
    name: "خدمات الترفيه",
    slug: "entertainment",
    description: "خدمات الترفيه والاشتراكات",
    image: "https://shop.ldrescdn.com/rms/ld-space/process/img/netflix_cover_art.webp",
    is_active: true,
    product_count: 2,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  }
]

// Mock Products with Variants
export const mockProducts: Product[] = [
  // Simple product with single variant (old PWOP)
  {
    id: "prod_1",
    name: "بطاقة ستيم 50 دولار",
    slug: "steam-gift-card-50",
    description: "بطاقة هدايا ستيم بقيمة 50 دولار أمريكي - تسليم فوري",
    main_image: "https://shop.ldrescdn.com/rms/ld-space/process/img/steam_gift_card_cover_art.webp",
    category_id: "cat_2",
    status: "active",
    has_variants: false,
    rating: 4.8,
    comment_count: 1250,
    featured: true,
    tags: ["steam", "gift-card", "digital"],
    variants: [
      {
        id: "var_1",
        product_id: "prod_1",
        name: "Default",
        original_price: 45.00,
        user_price: 50.00,
        discount_price: 47.50,
        distributor_price: 48.00,
        inventory_type: "digital_codes",
        available_codes: 150,
        is_default: true,
        sort_order: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      }
    ],
    custom_fields: [],
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },

  // Product with multiple variants (old PWP)
  {
    id: "prod_2",
    name: "هونكاي: ستار ريل",
    slug: "honkai-star-rail",
    description: "اشحن الجواهر النجمية وشظايا الأحلام لهونكاي ستار ريل فوراً!",
    main_image: "https://shop.ldrescdn.com/rms/ld-space/process/img/5ba76fd6bcce49d5a3f9e97c2c64b1691737079512.webp",
    category_id: "cat_1",
    status: "active",
    has_variants: true,
    rating: 5.0,
    comment_count: 125704,
    featured: true,
    tags: ["honkai", "rpg", "anime"],
    variants: [
      {
        id: "var_2",
        product_id: "prod_2",
        name: "60 جوهرة نجمية",
        image: "https://shop.ldrescdn.com/rms/ld-space/process/img/5ba76fd6bcce49d5a3f9e97c2c64b1691737079512.webp",
        original_price: 0.80,
        user_price: 0.99,
        discount_price: 0.89,
        distributor_price: 0.95,
        inventory_type: "digital_codes",
        available_codes: 500,
        is_default: true,
        sort_order: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      },
      {
        id: "var_3",
        product_id: "prod_2",
        name: "300 جوهرة نجمية",
        image: "https://shop.ldrescdn.com/rms/ld-space/process/img/5ba76fd6bcce49d5a3f9e97c2c64b1691737079512.webp",
        original_price: 4.00,
        user_price: 4.99,
        discount_price: 4.49,
        distributor_price: 4.75,
        inventory_type: "digital_codes",
        available_codes: 300,
        is_default: false,
        sort_order: 2,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      },
      {
        id: "var_4",
        product_id: "prod_2",
        name: "980 جوهرة نجمية",
        image: "https://shop.ldrescdn.com/rms/ld-space/process/img/5ba76fd6bcce49d5a3f9e97c2c64b1691737079512.webp",
        original_price: 12.00,
        user_price: 14.99,
        discount_price: 13.99,
        distributor_price: 14.25,
        inventory_type: "digital_codes",
        available_codes: 200,
        is_default: false,
        sort_order: 3,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      }
    ],
    custom_fields: [
      {
        id: "cf_1",
        product_id: "prod_2",
        field_name: "معرف اللاعب",
        field_description: "أدخل معرف اللاعب الخاص بك",
        field_type: "text",
        sort_order: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      },
      {
        id: "cf_2",
        product_id: "prod_2",
        field_name: "الخادم",
        field_description: "اختر الخادم الخاص بك",
        field_type: "dropdown",
        dropdown_options: ["آسيا", "أوروبا", "أمريكا", "TW/HK/MO"],
        sort_order: 2,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      }
    ],
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },

  // Service product with manual inventory
  {
    id: "prod_3",
    name: "اشتراك نتفليكس",
    slug: "netflix-subscription",
    description: "اشتراك نتفليكس شهري - حسابات مشتركة عالية الجودة",
    main_image: "https://shop.ldrescdn.com/rms/ld-space/process/img/netflix_cover_art.webp",
    category_id: "cat_4",
    status: "active",
    has_variants: true,
    rating: 4.5,
    comment_count: 890,
    featured: false,
    tags: ["netflix", "streaming", "subscription"],
    variants: [
      {
        id: "var_5",
        product_id: "prod_3",
        name: "خطة أساسية",
        original_price: 8.00,
        user_price: 12.99,
        distributor_price: 11.50,
        inventory_type: "manual",
        manual_quantity: 50,
        is_default: true,
        sort_order: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      },
      {
        id: "var_6",
        product_id: "prod_3",
        name: "خطة متقدمة",
        original_price: 12.00,
        user_price: 18.99,
        distributor_price: 16.50,
        inventory_type: "manual",
        manual_quantity: 30,
        is_default: false,
        sort_order: 2,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      },
      {
        id: "var_7",
        product_id: "prod_3",
        name: "خطة بريميوم",
        original_price: 15.00,
        user_price: 24.99,
        distributor_price: 21.50,
        inventory_type: "manual",
        manual_quantity: 20,
        is_default: false,
        sort_order: 3,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
      }
    ],
    custom_fields: [],
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  }
]

// Mock Digital Codes
export const mockDigitalCodes: DigitalCode[] = [
  // Steam gift card codes
  {
    id: "code_1",
    variant_id: "var_1",
    code: "STEAM-ABCD-EFGH-IJKL",
    status: "available",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  {
    id: "code_2",
    variant_id: "var_1",
    code: "STEAM-MNOP-QRST-UVWX",
    status: "available",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  // Honkai Star Rail codes
  {
    id: "code_3",
    variant_id: "var_2",
    code: "HSR60-ABCD-EFGH-IJKL",
    status: "available",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  },
  {
    id: "code_4",
    variant_id: "var_3",
    code: "HSR300-MNOP-QRST-UVWX",
    status: "used",
    order_id: "order_1",
    used_at: "2024-01-15T10:30:00Z",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-15T10:30:00Z"
  }
]
