<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" dir="rtl">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div 
        class="fixed inset-0 transition-opacity bg-black/50 backdrop-blur-sm"
        @click="$emit('close')"
      ></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl shadow-xl">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-bold text-white-force">
            {{ isEdit ? 'تعديل العملة' : 'إضافة عملة جديدة' }}
          </h3>
          <button 
            @click="$emit('close')"
            class="p-2 rounded-lg hover:bg-theme-surface-light transition-colors"
          >
            <svg class="w-5 h-5 text-theme-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- Currency Code -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              رمز العملة <span class="text-red-400">*</span>
            </label>
            <input 
              v-model="form.code"
              type="text"
              maxlength="3"
              placeholder="مثل: SAR"
              :disabled="isEdit"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary disabled:opacity-50 disabled:cursor-not-allowed"
              :class="{ 'border-red-500': errors.code }"
            />
            <p v-if="errors.code" class="text-red-400 text-xs mt-1">{{ errors.code }}</p>
            <p class="text-theme-text-muted text-xs mt-1">3 أحرف فقط (مثل: USD, SAR, AED)</p>
          </div>

          <!-- Currency Name -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              اسم العملة <span class="text-red-400">*</span>
            </label>
            <input 
              v-model="form.name"
              type="text"
              placeholder="مثل: ريال سعودي"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
              :class="{ 'border-red-500': errors.name }"
            />
            <p v-if="errors.name" class="text-red-400 text-xs mt-1">{{ errors.name }}</p>
          </div>

          <!-- Currency Symbol -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              رمز العملة <span class="text-red-400">*</span>
            </label>
            <input 
              v-model="form.symbol"
              type="text"
              placeholder="مثل: ر.س"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
              :class="{ 'border-red-500': errors.symbol }"
            />
            <p v-if="errors.symbol" class="text-red-400 text-xs mt-1">{{ errors.symbol }}</p>
          </div>

          <!-- Exchange Rate -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              سعر الصرف مقابل الدولار <span class="text-red-400">*</span>
            </label>
            <input 
              v-model.number="form.exchangeRate"
              type="number"
              step="0.0001"
              min="0.0001"
              placeholder="مثل: 3.75"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
              :class="{ 'border-red-500': errors.exchangeRate }"
            />
            <p v-if="errors.exchangeRate" class="text-red-400 text-xs mt-1">{{ errors.exchangeRate }}</p>
            <p class="text-theme-text-muted text-xs mt-1">
              كم {{ form.code || 'وحدة' }} = 1 دولار أمريكي
            </p>
          </div>

          <!-- Preview -->
          <div v-if="form.exchangeRate > 0" class="bg-theme-primary/10 border border-theme-primary/20 rounded-lg p-3">
            <h4 class="text-sm font-medium text-theme-primary mb-2">معاينة التحويل</h4>
            <div class="text-xs text-theme-text-secondary space-y-1">
              <p>1 USD = {{ form.exchangeRate }} {{ form.code || 'وحدة' }}</p>
              <p>100 USD = {{ (form.exchangeRate * 100).toFixed(2) }} {{ form.code || 'وحدة' }}</p>
              <p>1 {{ form.code || 'وحدة' }} = {{ (1 / form.exchangeRate).toFixed(4) }} USD</p>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
            <p class="text-red-400 text-sm">{{ error }}</p>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3 space-x-reverse pt-4">
            <button 
              type="button"
              @click="$emit('close')"
              class="btn-secondary px-4 py-2"
            >
              إلغاء
            </button>
            
            <button 
              type="submit"
              :disabled="loading || !isFormValid"
              class="btn-primary px-4 py-2 disabled:opacity-50"
            >
              <span v-if="loading">جاري الحفظ...</span>
              <span v-else>{{ isEdit ? 'تحديث' : 'إضافة' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Currency {
  id?: number
  code: string
  name: string
  symbol: string
  exchangeRate: number
}

interface Props {
  show: boolean
  currency?: Currency | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  save: [currency: Omit<Currency, 'id'>]
  update: [id: number, currency: Omit<Currency, 'id'>]
}>()

const loading = ref(false)
const error = ref('')

const form = reactive({
  code: '',
  name: '',
  symbol: '',
  exchangeRate: 0
})

const errors = reactive({
  code: '',
  name: '',
  symbol: '',
  exchangeRate: ''
})

const isEdit = computed(() => !!props.currency?.id)

// Helper functions
const resetForm = () => {
  form.code = ''
  form.name = ''
  form.symbol = ''
  form.exchangeRate = 0
}

const clearErrors = () => {
  errors.code = ''
  errors.name = ''
  errors.symbol = ''
  errors.exchangeRate = ''
}

// Watch for currency changes to populate form
watch(() => props.currency, (currency) => {
  if (currency) {
    form.code = currency.code
    form.name = currency.name
    form.symbol = currency.symbol
    form.exchangeRate = currency.exchangeRate
  } else {
    resetForm()
  }
}, { immediate: true })

// Watch for show changes to reset form
watch(() => props.show, (show) => {
  if (show && !props.currency) {
    resetForm()
  }
  if (show) {
    clearErrors()
    error.value = ''
  }
})

const isFormValid = computed(() => {
  return form.code.length === 3 &&
         form.name.trim().length > 0 &&
         form.symbol.trim().length > 0 &&
         form.exchangeRate > 0
})

const validateForm = () => {
  clearErrors()
  let isValid = true

  if (!form.code || form.code.length !== 3) {
    errors.code = 'رمز العملة يجب أن يكون 3 أحرف'
    isValid = false
  }

  if (!form.name.trim()) {
    errors.name = 'اسم العملة مطلوب'
    isValid = false
  }

  if (!form.symbol.trim()) {
    errors.symbol = 'رمز العملة مطلوب'
    isValid = false
  }

  if (!form.exchangeRate || form.exchangeRate <= 0) {
    errors.exchangeRate = 'سعر الصرف يجب أن يكون أكبر من صفر'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) return

  loading.value = true
  error.value = ''

  try {
    const currencyData = {
      code: form.code.toUpperCase(),
      name: form.name.trim(),
      symbol: form.symbol.trim(),
      exchangeRate: form.exchangeRate
    }

    if (isEdit.value && props.currency?.id) {
      emit('update', props.currency.id, currencyData)
    } else {
      emit('save', currencyData)
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'حدث خطأ غير متوقع'
  } finally {
    loading.value = false
  }
}

// Auto-uppercase currency code
watch(() => form.code, (newCode) => {
  form.code = newCode.toUpperCase()
})
</script>
