export default defineNuxtRouteMiddleware((to, from) => {
  // Development mode - allow access to admin panel for debugging
  // Remove this in production and uncomment the authentication checks below

  /* Production authentication (uncomment when ready):
  const { isAuthenticated, canAccessAdmin } = useAuthStore();

  // First check if user is authenticated
  if (!isAuthenticated) {
    return navigateTo('/?login=required');
  }

  // Then check if user has admin access
  if (!canAccessAdmin) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Access denied. Admin privileges required.'
    });
  }
  */
});
