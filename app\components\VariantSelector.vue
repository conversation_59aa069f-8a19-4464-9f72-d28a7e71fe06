<template>
  <div class="variant-selector">
    <!-- Single Variant (Hidden Selector) -->
    <div v-if="!shouldShowSelector" class="space-y-2">
      <div class="text-2xl font-bold text-theme-primary">
        ${{ displayPrice.toFixed(2) }}
      </div>
      <div
        v-if="hasDiscount"
        class="text-lg text-theme-text-muted line-through"
      >
        ${{ selectedVariant.user_price.toFixed(2) }}
      </div>
      <div class="text-sm text-theme-text-muted">
        {{ getStockText(selectedVariant) }}
      </div>
    </div>

    <!-- Multiple Variants (Visible Selector) -->
    <div v-else class="space-y-4">
      <h3 class="text-lg font-semibold text-white">{{ selectorTitle }}</h3>
      
      <!-- Grid Layout for Variants -->
      <div class="grid grid-cols-1 gap-3">
        <button
          v-for="variant in variants"
          :key="variant.id"
          @click="selectVariant(variant)"
          :disabled="!isVariantAvailable(variant)"
          :class="[
            'p-4 rounded-xl border-2 text-right transition-all',
            isSelected(variant)
              ? 'border-theme-primary bg-theme-primary/10'
              : 'border-theme-light bg-theme-surface hover:border-theme-primary/50',
            !isVariantAvailable(variant) && 'opacity-50 cursor-not-allowed'
          ]"
        >
          <div class="flex justify-between items-center">
            <!-- Variant Info -->
            <div class="flex-1">
              <div class="font-semibold text-white">{{ variant.name }}</div>
              <div class="text-sm text-theme-text-muted">
                {{ getStockText(variant) }}
              </div>
              <div v-if="variant.image" class="mt-2">
                <img 
                  :src="variant.image" 
                  :alt="variant.name"
                  class="w-12 h-12 rounded-lg object-cover"
                />
              </div>
            </div>
            
            <!-- Pricing -->
            <div class="text-left">
              <div class="font-bold text-theme-primary">
                ${{ getVariantDisplayPrice(variant).toFixed(2) }}
              </div>
              <div
                v-if="hasVariantDiscount(variant)"
                class="text-sm text-theme-text-muted line-through"
              >
                ${{ variant.user_price.toFixed(2) }}
              </div>
              <div
                v-if="hasVariantDiscount(variant)"
                class="text-xs text-red-400 font-medium"
              >
                -{{ getDiscountPercentage(variant) }}%
              </div>
            </div>
          </div>
        </button>
      </div>

      <!-- Selected Variant Summary -->
      <div v-if="selectedVariant" class="bg-theme-surface/50 rounded-xl p-4 border border-theme-light">
        <div class="flex justify-between items-center">
          <div>
            <div class="font-medium text-white">{{ selectedVariant.name }}</div>
            <div class="text-sm text-theme-text-muted">{{ getStockText(selectedVariant) }}</div>
          </div>
          <div class="text-right">
            <div class="text-xl font-bold text-theme-primary">
              ${{ displayPrice.toFixed(2) }}
            </div>
            <div
              v-if="hasDiscount"
              class="text-sm text-theme-text-muted line-through"
            >
              ${{ selectedVariant.user_price.toFixed(2) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ProductVariant } from '~/types/products'

interface Props {
  variants: ProductVariant[]
  modelValue?: ProductVariant | null
  selectorTitle?: string
  showSingleVariant?: boolean
}

interface Emits {
  (e: 'update:modelValue', variant: ProductVariant | null): void
  (e: 'variantSelected', variant: ProductVariant): void
}

const props = withDefaults(defineProps<Props>(), {
  selectorTitle: 'اختر الباقة',
  showSingleVariant: false
})

const emit = defineEmits<Emits>()

// Get products utilities
const { 
  getAvailableQuantity, 
  getStockStatus, 
  isInStock,
  calculateDiscount 
} = useProducts()

// Local selected variant
const selectedVariant = computed({
  get: () => props.modelValue || getDefaultVariant(),
  set: (value) => emit('update:modelValue', value)
})

// Computed properties
const shouldShowSelector = computed(() => {
  return props.showSingleVariant || (props.variants.length > 1)
})

const displayPrice = computed(() => {
  if (!selectedVariant.value) return 0
  return getVariantDisplayPrice(selectedVariant.value)
})

const hasDiscount = computed(() => {
  if (!selectedVariant.value) return false
  return hasVariantDiscount(selectedVariant.value)
})

// Methods
const getDefaultVariant = (): ProductVariant | null => {
  if (!props.variants.length) return null
  return props.variants.find(v => v.is_default) || props.variants[0]
}

const selectVariant = (variant: ProductVariant) => {
  if (!isVariantAvailable(variant)) return
  
  selectedVariant.value = variant
  emit('variantSelected', variant)
}

const isSelected = (variant: ProductVariant): boolean => {
  return selectedVariant.value?.id === variant.id
}

const isVariantAvailable = (variant: ProductVariant): boolean => {
  return isInStock(variant)
}

const getVariantDisplayPrice = (variant: ProductVariant): number => {
  return variant.discount_price && variant.discount_price < variant.user_price 
    ? variant.discount_price 
    : variant.user_price
}

const hasVariantDiscount = (variant: ProductVariant): boolean => {
  return !!(variant.discount_price && variant.discount_price < variant.user_price)
}

const getDiscountPercentage = (variant: ProductVariant): number => {
  if (!hasVariantDiscount(variant)) return 0
  return calculateDiscount(variant.user_price, variant.discount_price!)
}

const getStockText = (variant: ProductVariant): string => {
  const quantity = getAvailableQuantity(variant)
  const status = getStockStatus(variant)
  
  if (status === 'out_of_stock') return 'غير متوفر'
  if (status === 'low_stock') return `متبقي ${quantity} فقط`
  if (variant.inventory_type === 'digital_codes') return 'متوفر فوراً'
  return `متوفر (${quantity})`
}

// Initialize with default variant
onMounted(() => {
  if (!selectedVariant.value && props.variants.length) {
    const defaultVariant = getDefaultVariant()
    if (defaultVariant) {
      selectVariant(defaultVariant)
    }
  }
})

// Watch for variants changes
watch(() => props.variants, (newVariants) => {
  if (newVariants.length && !selectedVariant.value) {
    const defaultVariant = getDefaultVariant()
    if (defaultVariant) {
      selectVariant(defaultVariant)
    }
  }
}, { immediate: true })
</script>

<style scoped>
.variant-selector {
  /* Custom styles for variant selector */
}

/* Hover effects for variant buttons */
.variant-selector button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Selected variant highlight */
.variant-selector button.border-theme-primary {
  box-shadow: 0 0 0 1px rgba(var(--color-primary), 0.3);
}

/* Disabled variant styling */
.variant-selector button:disabled {
  transform: none;
  box-shadow: none;
}
</style>
