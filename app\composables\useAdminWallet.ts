export const useAdminWallet = () => {
  const transactions = ref([
    {
      id: 'TXN-001',
      userId: 1,
      userName: 'أحمد محمد',
      userEmail: '<EMAIL>',
      type: 'deposit',
      paidCurrency: 'SAR',
      amount: 375.00,
      exchangeRate: 3.75,
      usdEquivalent: 100.00,
      status: 'completed',
      method: 'bank_transfer',
      createdAt: '2024-07-30T09:15:00Z',
      completedAt: '2024-07-30T09:45:00Z',
      reference: 'BT-2024-001',
      notes: 'تحويل بنكي من البنك الأهلي'
    },
    {
      id: 'TXN-002',
      userId: 2,
      userName: 'فاطمة علي',
      userEmail: '<EMAIL>',
      type: 'withdrawal',
      paidCurrency: 'USD',
      amount: 50.00,
      exchangeRate: 1.0,
      usdEquivalent: 50.00,
      status: 'pending',
      method: 'paypal',
      createdAt: '2024-07-30T11:20:00Z',
      completedAt: null,
      reference: 'PP-2024-002',
      notes: 'سحب إلى حساب PayPal'
    },
    {
      id: 'TXN-003',
      userId: 3,
      userName: 'محمد حسن',
      userEmail: '<EMAIL>',
      type: 'deposit',
      paidCurrency: 'AED',
      amount: 183.50,
      exchangeRate: 3.67,
      usdEquivalent: 50.00,
      status: 'completed',
      method: 'credit_card',
      createdAt: '2024-07-29T14:30:00Z',
      completedAt: '2024-07-29T14:32:00Z',
      reference: 'CC-2024-003',
      notes: 'دفع بالبطاقة الائتمانية'
    },
    {
      id: 'TXN-004',
      userId: 5,
      userName: 'خالد عبدالله',
      userEmail: '<EMAIL>',
      type: 'deposit',
      paidCurrency: 'SAR',
      amount: 750.00,
      exchangeRate: 3.75,
      usdEquivalent: 200.00,
      status: 'completed',
      method: 'bank_transfer',
      createdAt: '2024-07-29T10:15:00Z',
      completedAt: '2024-07-29T11:00:00Z',
      reference: 'BT-2024-004',
      notes: 'تحويل بنكي من بنك الراجحي'
    },
    {
      id: 'TXN-005',
      userId: 2,
      userName: 'فاطمة علي',
      userEmail: '<EMAIL>',
      type: 'withdrawal',
      paidCurrency: 'SAR',
      amount: 187.50,
      exchangeRate: 3.75,
      usdEquivalent: 50.00,
      status: 'declined',
      method: 'bank_transfer',
      createdAt: '2024-07-28T16:45:00Z',
      completedAt: null,
      reference: 'BT-2024-005',
      notes: 'رفض بسبب عدم توفر الرصيد الكافي'
    },
    {
      id: 'TXN-006',
      userId: 1,
      userName: 'أحمد محمد',
      userEmail: '<EMAIL>',
      type: 'deposit',
      paidCurrency: 'USD',
      amount: 75.00,
      exchangeRate: 1.0,
      usdEquivalent: 75.00,
      status: 'completed',
      method: 'paypal',
      createdAt: '2024-07-28T12:20:00Z',
      completedAt: '2024-07-28T12:22:00Z',
      reference: 'PP-2024-006',
      notes: 'دفع عبر PayPal'
    }
  ])

  const loading = ref(false)
  const error = ref<string | null>(null)

  // Filters
  const filters = ref({
    type: '',
    currency: '',
    status: '',
    dateFrom: '',
    dateTo: '',
    search: ''
  })

  // Computed filtered transactions
  const filteredTransactions = computed(() => {
    let result = transactions.value

    if (filters.value.type) {
      result = result.filter(txn => txn.type === filters.value.type)
    }

    if (filters.value.currency) {
      result = result.filter(txn => txn.paidCurrency === filters.value.currency)
    }

    if (filters.value.status) {
      result = result.filter(txn => txn.status === filters.value.status)
    }

    if (filters.value.dateFrom) {
      result = result.filter(txn => 
        new Date(txn.createdAt) >= new Date(filters.value.dateFrom)
      )
    }

    if (filters.value.dateTo) {
      result = result.filter(txn => 
        new Date(txn.createdAt) <= new Date(filters.value.dateTo)
      )
    }

    if (filters.value.search) {
      const search = filters.value.search.toLowerCase()
      result = result.filter(txn => 
        txn.id.toLowerCase().includes(search) ||
        txn.userName.toLowerCase().includes(search) ||
        txn.reference.toLowerCase().includes(search)
      )
    }

    return result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  })

  // Stats
  const stats = computed(() => {
    const completedTransactions = transactions.value.filter(t => t.status === 'completed')
    const deposits = completedTransactions.filter(t => t.type === 'deposit')
    const withdrawals = completedTransactions.filter(t => t.type === 'withdrawal')
    
    const totalDeposits = deposits.reduce((sum, txn) => sum + txn.usdEquivalent, 0)
    const totalWithdrawals = withdrawals.reduce((sum, txn) => sum + txn.usdEquivalent, 0)
    const netBalance = totalDeposits - totalWithdrawals
    
    const pendingTransactions = transactions.value.filter(t => t.status === 'pending').length

    return {
      totalTransactions: transactions.value.length,
      totalDeposits,
      totalWithdrawals,
      netBalance,
      pendingTransactions,
      completedTransactions: completedTransactions.length
    }
  })

  // Currency breakdown
  const currencyBreakdown = computed(() => {
    const breakdown: Record<string, { deposits: number, withdrawals: number, count: number }> = {}
    
    transactions.value
      .filter(t => t.status === 'completed')
      .forEach(txn => {
        if (!breakdown[txn.paidCurrency]) {
          breakdown[txn.paidCurrency] = { deposits: 0, withdrawals: 0, count: 0 }
        }
        
        breakdown[txn.paidCurrency].count++
        
        if (txn.type === 'deposit') {
          breakdown[txn.paidCurrency].deposits += txn.usdEquivalent
        } else {
          breakdown[txn.paidCurrency].withdrawals += txn.usdEquivalent
        }
      })
    
    return breakdown
  })

  // Methods
  const getTransactionById = (id: string) => {
    return transactions.value.find(txn => txn.id === id)
  }

  const updateTransactionStatus = async (txnId: string, newStatus: string, notes?: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const transaction = transactions.value.find(t => t.id === txnId)
      if (transaction) {
        transaction.status = newStatus
        if (notes) {
          transaction.notes = notes
        }
        if (newStatus === 'completed') {
          transaction.completedAt = new Date().toISOString()
        }
      }
    } catch (err) {
      error.value = 'فشل في تحديث حالة المعاملة'
      throw err
    } finally {
      loading.value = false
    }
  }

  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    Object.assign(filters.value, newFilters)
  }

  const clearFilters = () => {
    filters.value = {
      type: '',
      currency: '',
      status: '',
      dateFrom: '',
      dateTo: '',
      search: ''
    }
  }

  // Export to CSV
  const exportToCSV = () => {
    const headers = [
      'Transaction ID',
      'User Name',
      'Type',
      'Currency',
      'Amount',
      'USD Equivalent',
      'Exchange Rate',
      'Status',
      'Method',
      'Date',
      'Reference'
    ]
    
    const csvData = filteredTransactions.value.map(txn => [
      txn.id,
      txn.userName,
      txn.type,
      txn.paidCurrency,
      txn.amount,
      txn.usdEquivalent,
      txn.exchangeRate,
      txn.status,
      txn.method,
      new Date(txn.createdAt).toLocaleDateString('ar-SA'),
      txn.reference
    ])
    
    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `wallet-transactions-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return {
    transactions: readonly(transactions),
    filteredTransactions,
    stats,
    currencyBreakdown,
    loading: readonly(loading),
    error: readonly(error),
    filters: readonly(filters),
    getTransactionById,
    updateTransactionStatus,
    setFilters,
    clearFilters,
    exportToCSV
  }
}
