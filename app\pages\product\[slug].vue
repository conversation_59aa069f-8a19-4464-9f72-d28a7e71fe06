<template>
  <div class="min-h-screen bg-theme-background" dir="rtl">
    <div class="container mx-auto px-4 py-6">
      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center min-h-96">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-theme-primary"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-400 mb-4">
          <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <h2 class="text-xl font-bold text-white mb-2">المنتج غير موجود</h2>
        <p class="text-theme-text-muted mb-4">{{ error }}</p>
        <NuxtLink to="/shop" class="btn btn-primary">
          العودة للمتجر
        </NuxtLink>
      </div>

      <!-- Product Details -->
      <div v-else-if="product" class="max-w-6xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <!-- Product Image -->
          <div class="space-y-4">
            <div class="aspect-square overflow-hidden rounded-2xl bg-theme-surface">
              <img
                :src="currentImage"
                :alt="product.name"
                class="w-full h-full object-cover"
              />
            </div>
            
            <!-- Additional Images (if variants have images) -->
            <div v-if="variantImages.length > 1" class="flex space-x-2 space-x-reverse">
              <button
                v-for="(image, index) in variantImages"
                :key="index"
                @click="currentImage = image"
                :class="[
                  'w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors',
                  currentImage === image ? 'border-theme-primary' : 'border-theme-light'
                ]"
              >
                <img :src="image" :alt="`صورة ${index + 1}`" class="w-full h-full object-cover" />
              </button>
            </div>
          </div>

          <!-- Product Info -->
          <div class="space-y-6">
            <!-- Title and Rating -->
            <div>
              <h1 class="text-2xl lg:text-3xl font-bold text-white mb-2">{{ product.name }}</h1>
              <div v-if="product.rating" class="flex items-center space-x-2 space-x-reverse">
                <div class="flex">
                  <svg
                    v-for="i in 5"
                    :key="i"
                    :class="[
                      'w-4 h-4',
                      i <= Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-600'
                    ]"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <span class="text-theme-text-muted">{{ product.rating }} ({{ product.comment_count }} تقييم)</span>
              </div>
            </div>

            <!-- Badges -->
            <div v-if="productBadges.length" class="flex flex-wrap gap-2">
              <span
                v-for="badge in productBadges"
                :key="badge.type"
                :class="getBadgeClass(badge.type)"
                class="px-3 py-1 rounded-full text-sm font-medium"
              >
                {{ badge.text }}
              </span>
            </div>

            <!-- Variant Selection -->
            <div v-if="shouldShowVariantSelector">
              <h3 class="text-lg font-semibold text-white mb-3">اختر الباقة</h3>
              <div class="grid grid-cols-1 gap-3">
                <button
                  v-for="variant in product.variants"
                  :key="variant.id"
                  @click="selectedVariant = variant"
                  :class="[
                    'p-4 rounded-xl border-2 text-right transition-all',
                    selectedVariant?.id === variant.id
                      ? 'border-theme-primary bg-theme-primary/10'
                      : 'border-theme-light bg-theme-surface hover:border-theme-primary/50'
                  ]"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <div class="font-semibold text-white">{{ variant.name }}</div>
                      <div class="text-sm text-theme-text-muted">
                        {{ getStockText(variant) }}
                      </div>
                    </div>
                    <div class="text-left">
                      <div class="font-bold text-theme-primary">
                        ${{ getVariantDisplayPrice(variant).toFixed(2) }}
                      </div>
                      <div
                        v-if="variant.discount_price && variant.discount_price < variant.user_price"
                        class="text-sm text-theme-text-muted line-through"
                      >
                        ${{ variant.user_price.toFixed(2) }}
                      </div>
                    </div>
                  </div>
                </button>
              </div>
            </div>

            <!-- Price Display for Single Variant -->
            <div v-else class="space-y-2">
              <div class="text-3xl font-bold text-theme-primary">
                ${{ getVariantDisplayPrice(selectedVariant!).toFixed(2) }}
              </div>
              <div
                v-if="selectedVariant?.discount_price && selectedVariant.discount_price < selectedVariant.user_price"
                class="text-xl text-theme-text-muted line-through"
              >
                ${{ selectedVariant.user_price.toFixed(2) }}
              </div>
              <div class="text-sm text-theme-text-muted">
                {{ getStockText(selectedVariant!) }}
              </div>
            </div>

            <!-- Custom Fields -->
            <div v-if="product.custom_fields?.length" class="space-y-4">
              <h3 class="text-lg font-semibold text-white">معلومات إضافية</h3>
              <div
                v-for="field in product.custom_fields"
                :key="field.id"
                class="space-y-2"
              >
                <label class="block text-sm font-medium text-theme-text-muted">
                  {{ field.field_name }}
                  <span v-if="field.field_description" class="text-xs text-theme-text-muted block">
                    {{ field.field_description }}
                  </span>
                </label>
                
                <!-- Text Field -->
                <input
                  v-if="field.field_type === 'text'"
                  v-model="customFieldValues[field.id]"
                  type="text"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                  :placeholder="field.field_description"
                />
                
                <!-- Dropdown Field -->
                <select
                  v-else-if="field.field_type === 'dropdown'"
                  v-model="customFieldValues[field.id]"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                >
                  <option value="">اختر...</option>
                  <option
                    v-for="option in field.dropdown_options"
                    :key="option"
                    :value="option"
                  >
                    {{ option }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Purchase Button -->
            <div class="space-y-4">
              <button
                @click="handlePurchase"
                :disabled="!canPurchase"
                :class="[
                  'w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all',
                  canPurchase
                    ? 'bg-theme-primary hover:bg-theme-primary-hover text-white'
                    : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                ]"
              >
                {{ purchaseButtonText }}
              </button>
              
              <div class="text-center text-sm text-theme-text-muted">
                تسليم فوري • دعم فني 24/7
              </div>
            </div>
          </div>
        </div>

        <!-- Product Description -->
        <div class="bg-theme-surface rounded-2xl p-6">
          <h2 class="text-xl font-bold text-white mb-4">وصف المنتج</h2>
          <div class="text-theme-text-secondary leading-relaxed whitespace-pre-line">
            {{ product.description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { mockProducts } from '~/data/mockProductsData'
import type { Product, ProductVariant } from '~/types/products'

// Get route params
const route = useRoute()
const slug = route.params.slug as string

// State
const loading = ref(false)
const error = ref<string | null>(null)
const product = ref<Product | null>(null)
const selectedVariant = ref<ProductVariant | null>(null)
const currentImage = ref('')
const customFieldValues = ref<Record<string, string>>({})

// Find product by slug (using mock data for now)
const findProduct = () => {
  const foundProduct = mockProducts.find(p => p.slug === slug)
  if (!foundProduct) {
    error.value = 'المنتج غير موجود'
    return
  }
  
  product.value = foundProduct
  selectedVariant.value = foundProduct.variants.find(v => v.is_default) || foundProduct.variants[0]
  currentImage.value = foundProduct.main_image
}

// Initialize
onMounted(() => {
  findProduct()
})

// SEO Meta
useSeoMeta({
  title: () => product.value ? `${product.value.name} - بنتاكون` : 'منتج - بنتاكون',
  description: () => product.value?.description || 'منتج من متجر بنتاكون',
})

// Computed properties
const shouldShowVariantSelector = computed(() => {
  return product.value?.has_variants && product.value.variants.length > 1
})

const variantImages = computed(() => {
  if (!product.value) return []
  const images = [product.value.main_image]
  product.value.variants.forEach(variant => {
    if (variant.image && !images.includes(variant.image)) {
      images.push(variant.image)
    }
  })
  return images
})

const productBadges = computed(() => {
  if (!product.value) return []
  const { getProductBadges } = useProducts()
  return getProductBadges(product.value)
})

const canPurchase = computed(() => {
  if (!selectedVariant.value) return false
  const { isInStock } = useProducts()
  return isInStock(selectedVariant.value)
})

const purchaseButtonText = computed(() => {
  if (!selectedVariant.value) return 'اختر باقة'
  if (!canPurchase.value) return 'غير متوفر'
  return 'شراء الآن'
})

// Methods
const { 
  getAvailableQuantity, 
  getStockStatus, 
  formatPrice,
  calculateDiscount 
} = useProducts()

const getVariantDisplayPrice = (variant: ProductVariant) => {
  return variant.discount_price && variant.discount_price < variant.user_price 
    ? variant.discount_price 
    : variant.user_price
}

const getStockText = (variant: ProductVariant) => {
  const quantity = getAvailableQuantity(variant)
  const status = getStockStatus(variant)
  
  if (status === 'out_of_stock') return 'غير متوفر'
  if (status === 'low_stock') return `متبقي ${quantity} فقط`
  if (variant.inventory_type === 'digital_codes') return 'متوفر فوراً'
  return `متوفر (${quantity})`
}

const getBadgeClass = (type: string) => {
  switch (type) {
    case 'featured':
      return 'bg-theme-primary text-white'
    case 'discount':
      return 'bg-red-500 text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}

const handlePurchase = () => {
  if (!canPurchase.value || !selectedVariant.value) return
  
  // TODO: Implement purchase logic
  console.log('Purchase:', {
    product: product.value,
    variant: selectedVariant.value,
    customFields: customFieldValues.value
  })
  
  // For now, just show an alert
  alert('سيتم تنفيذ عملية الشراء قريباً!')
}

// Watch for variant changes to update image
watch(selectedVariant, (newVariant) => {
  if (newVariant?.image) {
    currentImage.value = newVariant.image
  }
})
</script>
