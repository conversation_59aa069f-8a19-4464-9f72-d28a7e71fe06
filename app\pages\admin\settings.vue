<template>
  <div class="space-y-6" dir="rtl">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-white-force">إعدادات النظام</h1>
        <p class="text-theme-text-muted">إدارة إعدادات المنصة والتكوين العام</p>
      </div>
    </div>

    <!-- Settings Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- General Settings -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <h3 class="text-lg font-semibold text-white-force mb-4">الإعدادات العامة</h3>
        
        <div class="space-y-4">
          <!-- Site Name -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              اسم الموقع
            </label>
            <input 
              v-model="settings.siteName"
              type="text"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            />
          </div>
          
          <!-- Site Description -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              وصف الموقع
            </label>
            <textarea 
              v-model="settings.siteDescription"
              rows="3"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            ></textarea>
          </div>
          
          <!-- Contact Email -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              البريد الإلكتروني للتواصل
            </label>
            <input 
              v-model="settings.contactEmail"
              type="email"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            />
          </div>
          
          <!-- Support Phone -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              رقم الدعم الفني
            </label>
            <input 
              v-model="settings.supportPhone"
              type="tel"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            />
          </div>
        </div>
      </div>

      <!-- Payment Settings -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <h3 class="text-lg font-semibold text-white-force mb-4">إعدادات الدفع</h3>
        
        <div class="space-y-4">
          <!-- Default Currency -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              العملة الافتراضية
            </label>
            <select 
              v-model="settings.defaultCurrency"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            >
              <option value="USD">دولار أمريكي (USD)</option>
              <option value="SAR">ريال سعودي (SAR)</option>
              <option value="AED">درهم إماراتي (AED)</option>
            </select>
          </div>
          
          <!-- Minimum Order Amount -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              أقل مبلغ للطلب (USD)
            </label>
            <input 
              v-model.number="settings.minOrderAmount"
              type="number"
              min="0"
              step="0.01"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            />
          </div>
          
          <!-- Maximum Order Amount -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              أقصى مبلغ للطلب (USD)
            </label>
            <input 
              v-model.number="settings.maxOrderAmount"
              type="number"
              min="0"
              step="0.01"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            />
          </div>
          
          <!-- Payment Methods -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              طرق الدفع المفعلة
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input 
                  v-model="settings.paymentMethods.bankTransfer"
                  type="checkbox"
                  class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-theme-text-secondary">التحويل البنكي</span>
              </label>
              <label class="flex items-center">
                <input 
                  v-model="settings.paymentMethods.creditCard"
                  type="checkbox"
                  class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-theme-text-secondary">البطاقة الائتمانية</span>
              </label>
              <label class="flex items-center">
                <input 
                  v-model="settings.paymentMethods.paypal"
                  type="checkbox"
                  class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-theme-text-secondary">PayPal</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Settings -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <h3 class="text-lg font-semibold text-white-force mb-4">إعدادات الطلبات</h3>
        
        <div class="space-y-4">
          <!-- Auto Assignment -->
          <div>
            <label class="flex items-center">
              <input 
                v-model="settings.autoAssignOrders"
                type="checkbox"
                class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
              />
              <span class="mr-2 text-theme-text-secondary">تعيين الطلبات تلقائياً للعمال</span>
            </label>
          </div>
          
          <!-- Order Timeout -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              مهلة إنجاز الطلب (بالساعات)
            </label>
            <input 
              v-model.number="settings.orderTimeout"
              type="number"
              min="1"
              max="72"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            />
          </div>
          
          <!-- Max Orders Per Worker -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              أقصى عدد طلبات للعامل الواحد
            </label>
            <input 
              v-model.number="settings.maxOrdersPerWorker"
              type="number"
              min="1"
              max="50"
              class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
            />
          </div>
          
          <!-- Require Delivery Code -->
          <div>
            <label class="flex items-center">
              <input 
                v-model="settings.requireDeliveryCode"
                type="checkbox"
                class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
              />
              <span class="mr-2 text-theme-text-secondary">يتطلب كود تسليم لإكمال الطلب</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <h3 class="text-lg font-semibold text-white-force mb-4">إعدادات الإشعارات</h3>
        
        <div class="space-y-4">
          <!-- Email Notifications -->
          <div>
            <label class="block text-sm font-medium text-theme-text-secondary mb-2">
              الإشعارات عبر البريد الإلكتروني
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input 
                  v-model="settings.notifications.newOrder"
                  type="checkbox"
                  class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-theme-text-secondary">طلب جديد</span>
              </label>
              <label class="flex items-center">
                <input 
                  v-model="settings.notifications.orderCompleted"
                  type="checkbox"
                  class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-theme-text-secondary">إكمال الطلب</span>
              </label>
              <label class="flex items-center">
                <input 
                  v-model="settings.notifications.walletTransaction"
                  type="checkbox"
                  class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-theme-text-secondary">معاملة محفظة</span>
              </label>
              <label class="flex items-center">
                <input 
                  v-model="settings.notifications.newUser"
                  type="checkbox"
                  class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-theme-text-secondary">مستخدم جديد</span>
              </label>
            </div>
          </div>
          
          <!-- SMS Notifications -->
          <div>
            <label class="flex items-center">
              <input 
                v-model="settings.smsNotifications"
                type="checkbox"
                class="rounded border-theme-light text-theme-primary focus:ring-theme-primary"
              />
              <span class="mr-2 text-theme-text-secondary">تفعيل الإشعارات عبر الرسائل النصية</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Save Button -->
    <div class="flex justify-end">
      <button 
        @click="saveSettings"
        :disabled="loading"
        class="btn-primary px-6 py-3"
      >
        <span v-if="loading">جاري الحفظ...</span>
        <span v-else>حفظ الإعدادات</span>
      </button>
    </div>

    <!-- Success Message -->
    <div v-if="showSuccess" class="fixed bottom-4 left-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
      تم حفظ الإعدادات بنجاح
    </div>
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'admin'
})

// State
const loading = ref(false)
const showSuccess = ref(false)

// Settings data
const settings = ref({
  siteName: 'بنتاكون',
  siteDescription: 'أفضل متجر للألعاب الرقمية وبطاقات الهدايا في الشرق الأوسط',
  contactEmail: '<EMAIL>',
  supportPhone: '+966 50 123 4567',
  defaultCurrency: 'USD',
  minOrderAmount: 5.00,
  maxOrderAmount: 1000.00,
  paymentMethods: {
    bankTransfer: true,
    creditCard: true,
    paypal: true
  },
  autoAssignOrders: true,
  orderTimeout: 24,
  maxOrdersPerWorker: 10,
  requireDeliveryCode: true,
  notifications: {
    newOrder: true,
    orderCompleted: true,
    walletTransaction: true,
    newUser: false
  },
  smsNotifications: false
})

// Methods
const saveSettings = async () => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Show success message
    showSuccess.value = true
    setTimeout(() => {
      showSuccess.value = false
    }, 3000)
    
    console.log('Settings saved:', settings.value)
  } catch (error) {
    console.error('Failed to save settings:', error)
  } finally {
    loading.value = false
  }
}

// Load settings on mount
onMounted(() => {
  // In a real app, load settings from API
  console.log('Loading settings...')
})
</script>
