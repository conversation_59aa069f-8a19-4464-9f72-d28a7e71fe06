export const useAdminUsers = () => {
  const users = ref([
    {
      id: 1,
      name: 'أحمد محمد',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      walletBalances: {
        USD: 1250.00,
        SAR: 4687.50,
        AED: 4593.75
      },
      joinedAt: '2024-01-15',
      lastActive: '2024-07-30',
      ordersCount: 45,
      totalSpent: 2340.50, // USD
      workerStats: null
    },
    {
      id: 2,
      name: 'فاطمة علي',
      email: '<EMAIL>',
      role: 'worker',
      status: 'active',
      walletBalances: {
        USD: 850.00,
        SAR: 3187.50,
        AED: 3118.75
      },
      joinedAt: '2024-02-20',
      lastActive: '2024-07-30',
      ordersCount: 23,
      totalSpent: 1150.75, // USD
      workerStats: {
        assignedOrders: 156,
        completedOrders: 142,
        completionRate: 91.0,
        averageRating: 4.7,
        totalEarnings: 3420.00 // USD
      }
    },
    {
      id: 3,
      name: 'محمد حسن',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      walletBalances: {
        USD: 320.00,
        SAR: 1200.00,
        AED: 1176.00
      },
      joinedAt: '2024-03-10',
      lastActive: '2024-07-29',
      ordersCount: 12,
      totalSpent: 567.25, // USD
      workerStats: null
    },
    {
      id: 4,
      name: 'عائشة أحمد',
      email: '<EMAIL>',
      role: 'worker',
      status: 'suspended',
      walletBalances: {
        USD: 0.00,
        SAR: 0.00,
        AED: 0.00
      },
      joinedAt: '2024-01-05',
      lastActive: '2024-07-25',
      ordersCount: 8,
      totalSpent: 234.50, // USD
      workerStats: {
        assignedOrders: 45,
        completedOrders: 38,
        completionRate: 84.4,
        averageRating: 4.2,
        totalEarnings: 1250.00 // USD
      }
    },
    {
      id: 5,
      name: 'خالد عبدالله',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      walletBalances: {
        USD: 750.00,
        SAR: 2812.50,
        AED: 2756.25
      },
      joinedAt: '2024-04-12',
      lastActive: '2024-07-30',
      ordersCount: 28,
      totalSpent: 1890.75, // USD
      workerStats: null
    }
  ])

  const currencies = ref([
    { code: 'USD', name: 'دولار أمريكي', rate: 1.0, symbol: '$' },
    { code: 'SAR', name: 'ريال سعودي', rate: 3.75, symbol: 'ر.س' },
    { code: 'AED', name: 'درهم إماراتي', rate: 3.67, symbol: 'د.إ' }
  ])

  const loading = ref(false)
  const error = ref<string | null>(null)

  // Filters
  const filters = ref({
    role: '',
    status: '',
    search: ''
  })

  // Computed filtered users
  const filteredUsers = computed(() => {
    let result = users.value

    if (filters.value.role) {
      result = result.filter(user => user.role === filters.value.role)
    }

    if (filters.value.status) {
      result = result.filter(user => user.status === filters.value.status)
    }

    if (filters.value.search) {
      const search = filters.value.search.toLowerCase()
      result = result.filter(user => 
        user.name.toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search)
      )
    }

    return result
  })

  // Stats
  const stats = computed(() => ({
    totalUsers: users.value.length,
    activeUsers: users.value.filter(u => u.status === 'active').length,
    workers: users.value.filter(u => u.role === 'worker').length,
    admins: users.value.filter(u => u.role === 'admin').length,
    totalWalletBalance: users.value.reduce((sum, user) => sum + user.walletBalances.USD, 0)
  }))

  // Methods
  const getUserById = (id: number) => {
    return users.value.find(user => user.id === id)
  }

  const updateUserRole = async (userId: number, newRole: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const user = users.value.find(u => u.id === userId)
      if (user) {
        user.role = newRole
        
        // If changing to worker, add worker stats
        if (newRole === 'worker' && !user.workerStats) {
          user.workerStats = {
            assignedOrders: 0,
            completedOrders: 0,
            completionRate: 0,
            averageRating: 0,
            totalEarnings: 0
          }
        }
        
        // If changing from worker, remove worker stats
        if (newRole !== 'worker') {
          user.workerStats = null
        }
      }
    } catch (err) {
      error.value = 'فشل في تحديث دور المستخدم'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateUserStatus = async (userId: number, newStatus: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const user = users.value.find(u => u.id === userId)
      if (user) {
        user.status = newStatus
      }
    } catch (err) {
      error.value = 'فشل في تحديث حالة المستخدم'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateWalletBalance = async (userId: number, currency: string, amount: number) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const user = users.value.find(u => u.id === userId)
      if (user) {
        user.walletBalances[currency] = amount
      }
    } catch (err) {
      error.value = 'فشل في تحديث رصيد المحفظة'
      throw err
    } finally {
      loading.value = false
    }
  }

  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    Object.assign(filters.value, newFilters)
  }

  const clearFilters = () => {
    filters.value = {
      role: '',
      status: '',
      search: ''
    }
  }

  return {
    users,
    currencies: readonly(currencies),
    filteredUsers,
    stats,
    loading: readonly(loading),
    error: readonly(error),
    filters,
    getUserById,
    updateUserRole,
    updateUserStatus,
    updateWalletBalance,
    setFilters,
    clearFilters
  }
}
