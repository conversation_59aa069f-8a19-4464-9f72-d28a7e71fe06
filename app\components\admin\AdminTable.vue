<template>
  <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl sm:rounded-3xl p-4 sm:p-6 shadow-xl">
    <!-- Table Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-4 sm:mb-6">
      <h3 class="text-lg sm:text-xl font-semibold text-white-force">{{ title }}</h3>
      <div v-if="showActions" class="flex items-center space-x-2 sm:space-x-3 space-x-reverse">
        <slot name="header-actions"></slot>
      </div>
    </div>

    <!-- Filters Section -->
    <div v-if="showFilters" class="mb-4 sm:mb-6">
      <slot name="filters"></slot>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8 sm:py-12">
      <div class="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-theme-primary"></div>
      <span class="mr-2 sm:mr-3 text-sm sm:text-base text-theme-text-muted">جاري التحميل...</span>
    </div>

    <!-- Empty State -->
    <div v-else-if="!data || data.length === 0" class="text-center py-8 sm:py-12">
      <div class="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 rounded-full bg-theme-surface/30 flex items-center justify-center">
        <svg class="w-8 h-8 sm:w-10 sm:h-10 text-theme-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2m16-7H4m16 0l-2-2m2 2l-2 2M4 13l2-2m-2 2l2 2" />
        </svg>
      </div>
      <div class="text-theme-text-muted text-base sm:text-lg mb-2">{{ emptyMessage || 'لا توجد بيانات' }}</div>
      <div class="text-theme-text-muted text-sm">لم يتم العثور على أي عناصر</div>
    </div>

    <!-- Desktop Table -->
    <div v-else class="hidden lg:block overflow-x-auto rounded-xl border border-theme-light/30">
      <table class="w-full">
        <thead class="bg-theme-surface/20">
          <tr class="border-b border-theme-light/50">
            <th
              v-for="column in columns"
              :key="column.key"
              :class="[column.class, 'text-right py-4 px-4 text-sm font-semibold text-theme-text-muted uppercase tracking-wider']"
            >
              {{ column.label }}
            </th>
            <th v-if="showActions" class="text-right py-4 px-4 text-sm font-semibold text-theme-text-muted uppercase tracking-wider w-32">
              الإجراءات
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-theme-light/20">
          <tr
            v-for="(item, index) in data"
            :key="item.id || index"
            class="hover:bg-theme-surface/10 transition-all duration-200 group"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              :class="[column.class, 'py-4 px-4 whitespace-nowrap']"
            >
              <slot
                :name="`cell-${column.key}`"
                :item="item"
                :value="getNestedValue(item, column.key)"
              >
                <span class="text-white-force text-sm">{{ getNestedValue(item, column.key) }}</span>
              </slot>
            </td>
            <td v-if="showActions" class="py-4 px-4 whitespace-nowrap">
              <slot name="actions-cell" :item="item"></slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Tablet View -->
    <div v-else class="hidden md:block lg:hidden overflow-x-auto">
      <div class="space-y-3">
        <div
          v-for="(item, index) in data"
          :key="item.id || index"
          class="bg-theme-surface/20 rounded-xl p-4 border border-theme-light/30 hover:bg-theme-surface/30 transition-all duration-200"
        >
          <div class="grid grid-cols-2 gap-4">
            <div v-for="column in columns.slice(0, 4)" :key="column.key" class="space-y-1">
              <div class="text-xs text-theme-text-muted uppercase tracking-wider">{{ column.label }}</div>
              <slot
                :name="`cell-${column.key}`"
                :item="item"
                :value="getNestedValue(item, column.key)"
              >
                <div class="text-sm text-white-force">{{ getNestedValue(item, column.key) }}</div>
              </slot>
            </div>
          </div>
          <div v-if="showActions" class="flex justify-end pt-3 mt-3 border-t border-theme-light/30">
            <slot name="actions-cell" :item="item"></slot>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Cards -->
    <div v-else class="md:hidden space-y-3">
      <div
        v-for="(item, index) in data"
        :key="item.id || index"
        class="bg-theme-surface/20 rounded-xl p-4 border border-theme-light/30 shadow-sm"
      >
        <slot name="mobile-card" :item="item">
          <!-- Default mobile card layout -->
          <div class="space-y-3">
            <div v-for="column in columns" :key="column.key" class="flex justify-between items-start gap-3">
              <span class="text-theme-text-muted text-sm font-medium min-w-0 flex-shrink-0">{{ column.label }}</span>
              <div class="min-w-0 flex-1 text-left">
                <slot
                  :name="`cell-${column.key}`"
                  :item="item"
                  :value="getNestedValue(item, column.key)"
                >
                  <span class="text-white-force text-sm break-words">{{ getNestedValue(item, column.key) }}</span>
                </slot>
              </div>
            </div>
            <div v-if="showActions" class="flex justify-end pt-3 border-t border-theme-light/30">
              <slot name="actions-cell" :item="item"></slot>
            </div>
          </div>
        </slot>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="pagination && data && data.length > 0" class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mt-4 sm:mt-6 pt-4 border-t border-theme-light/30">
      <div class="text-xs sm:text-sm text-theme-text-muted text-center sm:text-right">
        عرض {{ pagination.from }} إلى {{ pagination.to }} من {{ pagination.total }} نتيجة
      </div>
      <div class="flex items-center justify-center sm:justify-end space-x-2 space-x-reverse">
        <button
          :disabled="!pagination.prevPage"
          @click="$emit('page-change', pagination.prevPage)"
          class="px-3 py-2 text-xs sm:text-sm border border-theme-light rounded-lg text-theme-text-muted hover:text-white-force hover:border-theme-primary hover:bg-theme-surface/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          السابق
        </button>
        <span class="px-3 py-2 text-xs sm:text-sm text-white-force bg-theme-surface/30 rounded-lg border border-theme-light/50">
          {{ pagination.currentPage }} / {{ pagination.totalPages }}
        </span>
        <button
          :disabled="!pagination.nextPage"
          @click="$emit('page-change', pagination.nextPage)"
          class="px-3 py-2 text-xs sm:text-sm border border-theme-light rounded-lg text-theme-text-muted hover:text-white-force hover:border-theme-primary hover:bg-theme-surface/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          التالي
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Column {
  key: string
  label: string
  class?: string
}

interface Pagination {
  currentPage: number
  totalPages: number
  from: number
  to: number
  total: number
  prevPage?: number
  nextPage?: number
}

interface Props {
  title: string
  columns: Column[]
  data: any[]
  loading?: boolean
  showFilters?: boolean
  showActions?: boolean
  emptyMessage?: string
  pagination?: Pagination
}

defineProps<Props>()

defineEmits<{
  'page-change': [page: number]
}>()

// Helper function to get nested object values
const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}
</script>
