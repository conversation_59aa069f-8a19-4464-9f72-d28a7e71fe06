<template>
  <div v-if="show" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50" dir="rtl">
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-6 border-b border-theme-light">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="w-12 h-12 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <div>
            <h2 class="text-xl font-bold text-white-force">{{ mode === 'view' ? 'تفاصيل المستخدم' : 'تعديل المستخدم' }}</h2>
            <p class="text-theme-text-muted">{{ user?.name || 'غير محدد' }}</p>
          </div>
        </div>
        <button @click="$emit('close')" class="text-theme-text-muted hover:text-theme-text-primary transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="overflow-y-auto max-h-[calc(90vh-120px)]">
        <div class="p-6 space-y-6">
          <!-- User Info Section -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bg-theme-surface/30 rounded-2xl p-4">
              <h3 class="text-lg font-semibold text-white-force mb-4">المعلومات الأساسية</h3>
              <div class="space-y-3">
                <div>
                  <label class="block text-sm font-medium text-theme-text-muted mb-1 flex items-center">
                    الاسم
                    <svg class="w-3 h-3 mr-1 text-theme-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                  </label>
                  <input
                    v-model="form.name"
                    readonly
                    type="text"
                    class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force opacity-60 cursor-not-allowed"
                    title="لا يمكن تعديل الاسم"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-theme-text-muted mb-1 flex items-center">
                    البريد الإلكتروني
                    <svg class="w-3 h-3 mr-1 text-theme-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                  </label>
                  <input
                    v-model="form.email"
                    readonly
                    type="email"
                    class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force opacity-60 cursor-not-allowed"
                    title="لا يمكن تعديل البريد الإلكتروني"
                  />
                </div>

                <!-- Note about locked fields -->
                <div class="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 text-yellow-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-sm text-yellow-300">
                      الاسم والبريد الإلكتروني لا يمكن تعديلهما لأسباب أمنية
                    </p>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-theme-text-muted mb-1">الدور</label>
                  <select 
                    v-model="form.role" 
                    :disabled="mode === 'view'"
                    class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
                    :class="{ 'opacity-60 cursor-not-allowed': mode === 'view' }"
                  >
                    <option value="admin">مدير</option>
                    <option value="worker">عامل</option>
                    <option value="user">مستخدم</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-theme-text-muted mb-1">الحالة</label>
                  <select 
                    v-model="form.status" 
                    :disabled="mode === 'view'"
                    class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
                    :class="{ 'opacity-60 cursor-not-allowed': mode === 'view' }"
                  >
                    <option value="active">نشط</option>
                    <option value="suspended">معلق</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Account Statistics -->
            <div class="bg-theme-surface/30 rounded-2xl p-4">
              <h3 class="text-lg font-semibold text-white-force mb-4">إحصائيات الحساب</h3>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-theme-text-muted">تاريخ الانضمام</span>
                  <span class="text-white-force">{{ formatDate(user?.joinedAt) }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-theme-text-muted">آخر نشاط</span>
                  <span class="text-white-force">{{ formatDate(user?.lastActive) }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-theme-text-muted">عدد الطلبات</span>
                  <span class="text-white-force">{{ user?.ordersCount || 0 }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-theme-text-muted">إجمالي المصروفات</span>
                  <span class="text-white-force">${{ user?.totalSpent || 0 }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Wallet Balances Section -->
          <div class="bg-theme-surface/30 rounded-2xl p-4">
            <h3 class="text-lg font-semibold text-white-force mb-4">أرصدة المحفظة</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div v-for="(balance, currency) in form.walletBalances" :key="currency" class="bg-theme-surface rounded-lg p-3">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-theme-text-muted text-sm">{{ currency }}</span>
                  <span class="text-xs text-theme-text-muted">{{ getCurrencySymbol(currency) }}</span>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                  <input 
                    v-model.number="form.walletBalances[currency]" 
                    :readonly="mode === 'view'"
                    type="number" 
                    step="0.01"
                    class="flex-1 px-2 py-1 bg-theme-background border border-theme-light rounded text-white-force text-sm focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    :class="{ 'opacity-60 cursor-not-allowed': mode === 'view' }"
                  />
                  <button 
                    v-if="mode === 'edit'"
                    @click="adjustBalance(currency, 'add')"
                    class="text-green-400 hover:text-green-300 p-1"
                    title="إضافة رصيد"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                  </button>
                  <button 
                    v-if="mode === 'edit'"
                    @click="adjustBalance(currency, 'subtract')"
                    class="text-red-400 hover:text-red-300 p-1"
                    title="خصم رصيد"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Worker Statistics (if applicable) -->
          <div v-if="user?.workerStats" class="bg-theme-surface/30 rounded-2xl p-4">
            <h3 class="text-lg font-semibold text-white-force mb-4">إحصائيات العامل</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-theme-primary">{{ user.workerStats.assignedOrders }}</div>
                <div class="text-sm text-theme-text-muted">الطلبات المُسندة</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-400">{{ user.workerStats.completedOrders }}</div>
                <div class="text-sm text-theme-text-muted">الطلبات المكتملة</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-400">{{ user.workerStats.completionRate }}%</div>
                <div class="text-sm text-theme-text-muted">معدل الإنجاز</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-yellow-400">{{ user.workerStats.averageRating }}/5</div>
                <div class="text-sm text-theme-text-muted">التقييم</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex items-center justify-between p-6 border-t border-theme-light">
        <div class="flex items-center space-x-3 space-x-reverse">
          <button 
            v-if="mode === 'view'"
            @click="mode = 'edit'"
            class="btn-primary px-4 py-2"
          >
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            تعديل
          </button>
          <button 
            v-if="mode === 'edit'"
            @click="saveChanges"
            :disabled="loading"
            class="btn-primary px-4 py-2"
          >
            <svg v-if="!loading" class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <svg v-else class="w-4 h-4 ml-2 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ loading ? 'جاري الحفظ...' : 'حفظ التغييرات' }}
          </button>
          <button 
            v-if="mode === 'edit'"
            @click="cancelEdit"
            class="btn-secondary px-4 py-2"
          >
            إلغاء
          </button>
        </div>
        
        <div class="flex items-center space-x-3 space-x-reverse">
          <button 
            @click="toggleUserStatus"
            :class="user?.status === 'active' ? 'btn-danger' : 'btn-success'"
            class="px-4 py-2"
          >
            {{ user?.status === 'active' ? 'تعليق المستخدم' : 'تفعيل المستخدم' }}
          </button>
          <button @click="$emit('close')" class="btn-secondary px-4 py-2">
            إغلاق
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
  user?: any
  initialMode?: 'view' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  initialMode: 'view'
})

const emit = defineEmits<{
  close: []
  userUpdated: [user: any]
  statusToggled: [userId: number, newStatus: string]
}>()

// State
const mode = ref(props.initialMode)
const loading = ref(false)
const originalForm = ref({})

// Form data
const form = reactive({
  name: '',      // Read-only - cannot be changed for security reasons
  email: '',     // Read-only - cannot be changed for security reasons
  role: '',
  status: '',
  walletBalances: {
    USD: 0,
    SAR: 0,
    AED: 0
  }
})

// Initialize form when user changes
watch(() => props.user, (newUser) => {
  if (newUser) {
    Object.assign(form, {
      name: newUser.name || '',
      email: newUser.email || '',
      role: newUser.role || '',
      status: newUser.status || '',
      walletBalances: { ...newUser.walletBalances } || { USD: 0, SAR: 0, AED: 0 }
    })
    originalForm.value = JSON.parse(JSON.stringify(form))
  }
}, { immediate: true })

// Reset mode when modal opens/closes
watch(() => props.show, (isShown) => {
  if (isShown) {
    mode.value = props.initialMode
  }
})

// Methods
const formatDate = (dateString?: string) => {
  if (!dateString) return 'غير محدد'
  return new Date(dateString).toLocaleDateString('ar-SA')
}

const getCurrencySymbol = (currency: string) => {
  const symbols = {
    USD: '$',
    SAR: 'ر.س',
    AED: 'د.إ'
  }
  return symbols[currency as keyof typeof symbols] || currency
}

const adjustBalance = (currency: string, action: 'add' | 'subtract') => {
  const amount = prompt(`كم المبلغ الذي تريد ${action === 'add' ? 'إضافته' : 'خصمه'}؟`)
  if (amount && !isNaN(Number(amount))) {
    const value = Number(amount)
    if (action === 'add') {
      form.walletBalances[currency] += value
    } else {
      form.walletBalances[currency] = Math.max(0, form.walletBalances[currency] - value)
    }
  }
}

const saveChanges = async () => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Emit the updated user data (excluding name and email which cannot be changed)
    const updatedUser = {
      ...props.user,
      role: form.role,
      status: form.status,
      walletBalances: { ...form.walletBalances }
    }

    emit('userUpdated', updatedUser)
    mode.value = 'view'
    originalForm.value = JSON.parse(JSON.stringify(form))
  } catch (error) {
    console.error('Failed to save changes:', error)
  } finally {
    loading.value = false
  }
}

const cancelEdit = () => {
  // Restore original form data
  Object.assign(form, JSON.parse(JSON.stringify(originalForm.value)))
  mode.value = 'view'
}

const toggleUserStatus = async () => {
  if (!props.user) return

  const newStatus = props.user.status === 'active' ? 'suspended' : 'active'
  const confirmMessage = newStatus === 'suspended'
    ? 'هل أنت متأكد من تعليق هذا المستخدم؟'
    : 'هل أنت متأكد من تفعيل هذا المستخدم؟'

  if (confirm(confirmMessage)) {
    emit('statusToggled', props.user.id, newStatus)
  }
}
</script>
