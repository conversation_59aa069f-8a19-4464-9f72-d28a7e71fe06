<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black/60 backdrop-blur-sm" @click="closeModal"></div>

    <!-- Modal -->
    <div class="relative min-h-screen flex items-center justify-center p-3 sm:p-4">
      <div class="relative bg-theme-surface rounded-2xl sm:rounded-3xl border border-theme-light max-w-5xl w-full max-h-[95vh] overflow-y-auto shadow-2xl">
        <!-- Header -->
        <div class="flex items-center justify-between p-4 sm:p-6 border-b border-theme-light">
          <h2 class="text-lg sm:text-xl font-bold text-white">
            {{ isEdit ? 'تعديل المنتج' : 'إضافة منتج جديد' }}
          </h2>
          <button
            @click="closeModal"
            class="text-theme-text-muted hover:text-white transition-all duration-200 p-2 rounded-xl hover:bg-theme-surface/30 active:scale-95"
          >
            <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="p-4 sm:p-6 space-y-4 sm:space-y-6">
          <!-- Basic Product Info -->
          <div class="space-y-3 sm:space-y-4">
            <h3 class="text-base sm:text-lg font-semibold text-white">معلومات المنتج الأساسية</h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
              <!-- Product Name -->
              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  اسم المنتج <span class="text-red-400">*</span>
                </label>
                <input
                  v-model="form.name"
                  type="text"
                  class="w-full px-3 py-2 text-sm sm:text-base bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary transition-all duration-200"
                  :class="{ 'border-red-500': errors.name }"
                />
                <p v-if="errors.name" class="text-red-400 text-xs mt-1">{{ errors.name }}</p>
              </div>

              <!-- Product Slug -->
              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  رابط المنتج <span class="text-red-400">*</span>
                </label>
                <input
                  v-model="form.slug"
                  type="text"
                  class="w-full px-3 py-2 text-sm sm:text-base bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary transition-all duration-200"
                  :class="{ 'border-red-500': errors.slug }"
                />
                <p v-if="errors.slug" class="text-red-400 text-xs mt-1">{{ errors.slug }}</p>
                <p class="text-theme-text-muted text-xs mt-1">سيتم استخدامه في رابط المنتج</p>
              </div>
            </div>

            <!-- Description -->
            <div>
              <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                وصف المنتج <span class="text-red-400">*</span>
              </label>
              <textarea
                v-model="form.description"
                rows="3"
                class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                :class="{ 'border-red-500': errors.description }"
              ></textarea>
              <p v-if="errors.description" class="text-red-400 text-xs mt-1">{{ errors.description }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Category -->
              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  الفئة <span class="text-red-400">*</span>
                </label>
                <select
                  v-model="form.category_id"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                  :class="{ 'border-red-500': errors.category_id }"
                >
                  <option value="">اختر الفئة</option>
                  <option
                    v-for="category in categories"
                    :key="category.id"
                    :value="category.id"
                  >
                    {{ category.name }}
                  </option>
                </select>
                <p v-if="errors.category_id" class="text-red-400 text-xs mt-1">{{ errors.category_id }}</p>
              </div>

              <!-- Status -->
              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  الحالة
                </label>
                <select
                  v-model="form.status"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                >
                  <option value="inactive">غير نشط</option>
                  <option value="active">نشط</option>
                </select>
              </div>
            </div>

            <!-- Image URL -->
            <div>
              <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                رابط الصورة <span class="text-red-400">*</span>
              </label>
              <input
                v-model="form.main_image"
                type="url"
                class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                :class="{ 'border-red-500': errors.main_image }"
              />
              <p v-if="errors.main_image" class="text-red-400 text-xs mt-1">{{ errors.main_image }}</p>
            </div>

            <!-- Product Options -->
            <div class="flex items-center space-x-4 space-x-reverse">
              <label class="flex items-center">
                <input
                  v-model="form.has_variants"
                  type="checkbox"
                  class="rounded border-theme-light bg-theme-surface text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-sm text-theme-text-secondary">منتج متعدد المتغيرات</span>
              </label>
              
              <label class="flex items-center">
                <input
                  v-model="form.featured"
                  type="checkbox"
                  class="rounded border-theme-light bg-theme-surface text-theme-primary focus:ring-theme-primary"
                />
                <span class="mr-2 text-sm text-theme-text-secondary">منتج مميز</span>
              </label>
            </div>
          </div>

          <!-- Variants Section -->
          <div v-if="form.has_variants" class="space-y-4">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-white">متغيرات المنتج</h3>
              <button
                @click="addVariant"
                class="btn-primary text-sm px-3 py-1"
              >
                إضافة متغير
              </button>
            </div>
            
            <div v-if="!variants.length" class="text-center py-8 text-theme-text-muted">
              لا توجد متغيرات. اضغط "إضافة متغير" لإضافة متغير جديد.
            </div>
            
            <div v-else class="space-y-3">
              <div
                v-for="(variant, index) in variants"
                :key="index"
                class="bg-theme-surface/50 rounded-xl p-4 border border-theme-light"
              >
                <div class="flex items-center justify-between mb-3">
                  <h4 class="font-medium text-white">متغير {{ index + 1 }}</h4>
                  <button
                    @click="removeVariant(index)"
                    class="text-red-400 hover:text-red-300 transition-colors"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label class="block text-xs text-theme-text-muted mb-1">اسم المتغير</label>
                    <input
                      v-model="variant.name"
                      type="text"
                      class="w-full px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    />
                  </div>

                  <div>
                    <label class="block text-xs text-theme-text-muted mb-1">صورة المتغير (اختياري)</label>
                    <input
                      v-model="variant.image"
                      type="url"
                      placeholder="رابط الصورة"
                      class="w-full px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    />
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mt-3">
                  <div>
                    <label class="block text-xs text-theme-text-muted mb-1">السعر الأصلي</label>
                    <input
                      v-model.number="variant.original_price"
                      type="number"
                      step="0.01"
                      class="w-full px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    />
                  </div>

                  <div>
                    <label class="block text-xs text-theme-text-muted mb-1">سعر المستخدم</label>
                    <input
                      v-model.number="variant.user_price"
                      type="number"
                      step="0.01"
                      class="w-full px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    />
                  </div>

                  <div>
                    <label class="block text-xs text-theme-text-muted mb-1">السعر المخفض (اختياري)</label>
                    <input
                      v-model.number="variant.discount_price"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      class="w-full px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    />
                  </div>

                  <div>
                    <label class="block text-xs text-theme-text-muted mb-1">سعر الموزع (اختياري)</label>
                    <input
                      v-model.number="variant.distributor_price"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      class="w-full px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    />
                  </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mt-3">
                  <div>
                    <label class="block text-xs text-theme-text-muted mb-1">نوع المخزون</label>
                    <select
                      v-model="variant.inventory_type"
                      class="w-full px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    >
                      <option value="manual">يدوي</option>
                      <option value="digital_codes">أكواد رقمية</option>
                    </select>
                  </div>
                  
                  <div v-if="variant.inventory_type === 'manual'">
                    <label class="block text-xs text-theme-text-muted mb-1">الكمية</label>
                    <input
                      v-model.number="variant.manual_quantity"
                      type="number"
                      class="w-full px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                    />
                  </div>

                  <div v-else-if="variant.inventory_type === 'digital_codes'">
                    <label class="block text-xs text-theme-text-muted mb-1">الأكواد الرقمية</label>
                    <div class="flex gap-2">
                      <input
                        v-model="variant.codes_input"
                        type="text"
                        placeholder="أدخل كود واحد أو عدة أكواد مفصولة بفواصل أو شرطات"
                        class="flex-1 px-2 py-1 text-sm bg-theme-surface border border-theme-light rounded text-white focus:outline-none focus:ring-1 focus:ring-theme-primary"
                        @keydown.enter="addCodesToVariant(variant, index)"
                      />
                      <button
                        @click="addCodesToVariant(variant, index)"
                        type="button"
                        class="px-3 py-1 text-xs bg-theme-primary text-white rounded hover:bg-theme-primary-hover"
                        :disabled="!variant.codes_input?.trim()"
                      >
                        إضافة كود
                      </button>
                    </div>

                    <!-- Preview next code -->
                    <div v-if="variant.codes_input?.trim()" class="mt-1 text-xs text-blue-400">
                      سيتم إضافة: "{{ getNextCodeToAdd(variant.codes_input) }}"
                    </div>

                    <!-- Show added codes -->
                    <div v-if="variant.digital_codes && variant.digital_codes.length" class="mt-2">
                      <div class="text-xs text-theme-text-muted mb-1">الأكواد المضافة: {{ variant.digital_codes.length }}</div>
                      <div class="max-h-20 overflow-y-auto bg-theme-surface/50 rounded p-2">
                        <div v-for="(code, codeIndex) in variant.digital_codes.slice(0, 5)" :key="codeIndex" class="text-xs text-theme-text-secondary flex justify-between items-center">
                          <span>{{ code }}</span>
                          <button
                            @click="removeCodeFromVariant(variant, codeIndex)"
                            class="text-red-400 hover:text-red-300 ml-2"
                            title="حذف الكود"
                          >
                            ×
                          </button>
                        </div>
                        <div v-if="variant.digital_codes.length > 5" class="text-xs text-theme-text-muted">
                          ... و {{ variant.digital_codes.length - 5 }} أكواد أخرى
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label class="flex items-center mt-4">
                      <input
                        v-model="variant.is_default"
                        type="checkbox"
                        class="rounded border-theme-light bg-theme-surface text-theme-primary focus:ring-theme-primary"
                        @change="setDefaultVariant(index)"
                      />
                      <span class="mr-2 text-xs text-theme-text-muted">متغير افتراضي</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Single Variant (when has_variants is false) -->
          <div v-else class="space-y-4">
            <h3 class="text-lg font-semibold text-white">تسعير المنتج</h3>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  السعر الأصلي <span class="text-red-400">*</span>
                </label>
                <input
                  v-model.number="defaultVariant.original_price"
                  type="number"
                  step="0.01"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  سعر المستخدم <span class="text-red-400">*</span>
                </label>
                <input
                  v-model.number="defaultVariant.user_price"
                  type="number"
                  step="0.01"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  السعر المخفض (اختياري)
                </label>
                <input
                  v-model.number="defaultVariant.discount_price"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  سعر الموزع (اختياري)
                </label>
                <input
                  v-model.number="defaultVariant.distributor_price"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                />
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  نوع المخزون
                </label>
                <select
                  v-model="defaultVariant.inventory_type"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                >
                  <option value="manual">يدوي</option>
                  <option value="digital_codes">أكواد رقمية</option>
                </select>
              </div>

              <div v-if="defaultVariant.inventory_type === 'manual'">
                <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                  الكمية
                </label>
                <input
                  v-model.number="defaultVariant.manual_quantity"
                  type="number"
                  class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                />
              </div>
            </div>

            <!-- Digital Codes for Single Product -->
            <div v-if="defaultVariant.inventory_type === 'digital_codes'" class="space-y-3">
              <label class="block text-sm font-medium text-theme-text-secondary">
                الأكواد الرقمية
              </label>
              <div class="flex gap-2">
                <input
                  v-model="defaultVariant.codes_input"
                  type="text"
                  placeholder="أدخل كود واحد أو عدة أكواد مفصولة بفواصل أو شرطات"
                  class="flex-1 px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-theme-primary"
                  @keydown.enter="addCodesToDefaultVariant"
                />
                <button
                  @click="addCodesToDefaultVariant"
                  type="button"
                  class="px-4 py-2 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover"
                  :disabled="!defaultVariant.codes_input?.trim()"
                >
                  إضافة كود
                </button>
              </div>

              <!-- Preview next code -->
              <div v-if="defaultVariant.codes_input?.trim()" class="text-sm text-blue-400">
                سيتم إضافة: "{{ getNextCodeToAdd(defaultVariant.codes_input) }}"
              </div>

              <!-- Show added codes -->
              <div v-if="defaultVariant.digital_codes && defaultVariant.digital_codes.length" class="mt-3">
                <div class="text-sm text-theme-text-muted mb-2">الأكواد المضافة: {{ defaultVariant.digital_codes.length }}</div>
                <div class="max-h-32 overflow-y-auto bg-theme-surface/50 rounded-lg p-3">
                  <div v-for="(code, codeIndex) in defaultVariant.digital_codes.slice(0, 10)" :key="codeIndex" class="text-sm text-theme-text-secondary mb-1 flex justify-between items-center">
                    <span>{{ code }}</span>
                    <button
                      @click="removeCodeFromDefault(codeIndex)"
                      class="text-red-400 hover:text-red-300 ml-2"
                      title="حذف الكود"
                    >
                      ×
                    </button>
                  </div>
                  <div v-if="defaultVariant.digital_codes.length > 10" class="text-sm text-theme-text-muted">
                    ... و {{ defaultVariant.digital_codes.length - 10 }} أكواد أخرى
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-end space-x-3 space-x-reverse p-6 border-t border-theme-light">
          <button
            @click="closeModal"
            class="btn-secondary px-4 py-2"
          >
            إلغاء
          </button>
          <button
            @click="saveProduct"
            :disabled="loading"
            class="btn-primary px-4 py-2"
          >
            {{ loading ? 'جاري الحفظ...' : (isEdit ? 'تحديث' : 'حفظ') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { mockCategories } from '~/data/mockProductsData'
import type { Product, ProductVariant, Category } from '~/types/products'

interface Props {
  isOpen: boolean
  product?: Product | null
}

interface Emits {
  (e: 'close'): void
  (e: 'save', product: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// State
const loading = ref(false)
const errors = ref<Record<string, string>>({})
const categories = ref<Category[]>(mockCategories)

// Form data
const form = ref({
  name: '',
  slug: '',
  description: '',
  main_image: '',
  category_id: '',
  status: 'inactive' as const,
  has_variants: false,
  featured: false
})

// Variants
const variants = ref<any[]>([])

// Default variant for single products
const defaultVariant = ref({
  name: 'Default',
  original_price: 0,
  user_price: 0,
  discount_price: null,
  distributor_price: null,
  inventory_type: 'manual' as const,
  manual_quantity: 0,
  digital_codes: [],
  codes_input: '',
  is_default: true
})

// Computed
const isEdit = computed(() => !!props.product)

// Methods
const closeModal = () => {
  emit('close')
}

const resetForm = () => {
  form.value = {
    name: '',
    slug: '',
    description: '',
    main_image: '',
    category_id: '',
    status: 'inactive',
    has_variants: false,
    featured: false
  }
  variants.value = []
  defaultVariant.value = {
    name: 'Default',
    original_price: 0,
    user_price: 0,
    discount_price: null,
    distributor_price: null,
    inventory_type: 'manual',
    manual_quantity: 0,
    digital_codes: [],
    codes_input: '',
    is_default: true
  }
  errors.value = {}
}

const loadProduct = () => {
  if (!props.product) {
    resetForm()
    return
  }

  const product = props.product
  form.value = {
    name: product.name,
    slug: product.slug,
    description: product.description,
    main_image: product.main_image,
    category_id: product.category_id,
    status: product.status,
    has_variants: product.has_variants,
    featured: product.featured || false
  }

  if (product.has_variants) {
    variants.value = product.variants.map(v => ({ ...v }))
  } else {
    const firstVariant = product.variants[0]
    if (firstVariant) {
      defaultVariant.value = {
        name: 'Default',
        original_price: firstVariant.original_price,
        user_price: firstVariant.user_price,
        inventory_type: firstVariant.inventory_type,
        manual_quantity: firstVariant.manual_quantity || 0,
        is_default: true
      }
    }
  }
}

const addVariant = () => {
  variants.value.push({
    name: `متغير ${variants.value.length + 1}`,
    image: '',
    original_price: 0,
    user_price: 0,
    discount_price: null,
    distributor_price: null,
    inventory_type: 'manual',
    manual_quantity: 0,
    digital_codes: [],
    codes_input: '',
    is_default: variants.value.length === 0
  })
}

const removeVariant = (index: number) => {
  variants.value.splice(index, 1)
  
  // Ensure at least one default variant
  if (variants.value.length && !variants.value.some(v => v.is_default)) {
    variants.value[0].is_default = true
  }
}

const setDefaultVariant = (index: number) => {
  variants.value.forEach((variant, i) => {
    variant.is_default = i === index
  })
}

const addCodesToVariant = (variant: any, index: number) => {
  if (!variant.codes_input?.trim()) return

  // Support both comma and dash separators
  const inputCodes = variant.codes_input
    .split(/[,-]/)
    .map((code: string) => code.trim())
    .filter((code: string) => code.length > 0)

  if (inputCodes.length === 0) return

  // Take only the FIRST code
  const singleCode = inputCodes[0]

  if (!variant.digital_codes) {
    variant.digital_codes = []
  }

  // Check for duplicates
  if (variant.digital_codes.includes(singleCode)) {
    console.warn('Code already exists:', singleCode)
    // Remove the duplicate from input and continue
    const remainingCodes = inputCodes.slice(1)
    variant.codes_input = remainingCodes.join(', ')
    return
  }

  // Add only one code
  variant.digital_codes.push(singleCode)

  // Remove the added code from input, keep the rest
  const remainingCodes = inputCodes.slice(1)
  variant.codes_input = remainingCodes.join(', ')
}

const addCodesToDefaultVariant = () => {
  if (!defaultVariant.value.codes_input?.trim()) return

  // Support both comma and dash separators
  const inputCodes = defaultVariant.value.codes_input
    .split(/[,-]/)
    .map((code: string) => code.trim())
    .filter((code: string) => code.length > 0)

  if (inputCodes.length === 0) return

  // Take only the FIRST code
  const singleCode = inputCodes[0]

  if (!defaultVariant.value.digital_codes) {
    defaultVariant.value.digital_codes = []
  }

  // Check for duplicates
  if (defaultVariant.value.digital_codes.includes(singleCode)) {
    console.warn('Code already exists:', singleCode)
    const remainingCodes = inputCodes.slice(1)
    defaultVariant.value.codes_input = remainingCodes.join(', ')
    return
  }

  // Add only one code
  defaultVariant.value.digital_codes.push(singleCode)

  // Remove the added code from input, keep the rest
  const remainingCodes = inputCodes.slice(1)
  defaultVariant.value.codes_input = remainingCodes.join(', ')
}

// Helper function for preview
const getNextCodeToAdd = (input: string): string => {
  if (!input?.trim()) return ''

  const codes = input
    .split(/[,-]/)
    .map((code: string) => code.trim())
    .filter((code: string) => code.length > 0)

  return codes.length > 0 ? codes[0] : ''
}

// Code removal functions
const removeCodeFromVariant = (variant: any, codeIndex: number) => {
  if (variant.digital_codes && variant.digital_codes.length > codeIndex) {
    variant.digital_codes.splice(codeIndex, 1)
  }
}

const removeCodeFromDefault = (codeIndex: number) => {
  if (defaultVariant.value.digital_codes && defaultVariant.value.digital_codes.length > codeIndex) {
    defaultVariant.value.digital_codes.splice(codeIndex, 1)
  }
}

const validateForm = () => {
  errors.value = {}
  
  if (!form.value.name.trim()) {
    errors.value.name = 'اسم المنتج مطلوب'
  }
  
  if (!form.value.slug.trim()) {
    errors.value.slug = 'رابط المنتج مطلوب'
  }
  
  if (!form.value.description.trim()) {
    errors.value.description = 'وصف المنتج مطلوب'
  }
  
  if (!form.value.main_image.trim()) {
    errors.value.main_image = 'صورة المنتج مطلوبة'
  }
  
  if (!form.value.category_id) {
    errors.value.category_id = 'فئة المنتج مطلوبة'
  }
  
  return Object.keys(errors.value).length === 0
}

const saveProduct = async () => {
  if (!validateForm()) return
  
  loading.value = true
  
  try {
    const productData = {
      ...form.value,
      variants: form.value.has_variants ? variants.value : [defaultVariant.value]
    }
    
    emit('save', productData)
    closeModal()
  } catch (error) {
    console.error('Error saving product:', error)
  } finally {
    loading.value = false
  }
}

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    loadProduct()
  }
})

// Auto-generate slug from name
watch(() => form.value.name, (name) => {
  if (name && !isEdit.value) {
    form.value.slug = name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }
})
</script>
