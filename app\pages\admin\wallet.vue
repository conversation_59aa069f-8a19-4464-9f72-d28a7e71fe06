<template>
  <div class="space-y-6" dir="rtl">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-white-force">معاملات المحفظة</h1>
        <p class="text-theme-text-muted">إدارة الإيداعات والسحوبات ومعاملات المحفظة</p>
      </div>
      
      <div class="flex items-center space-x-3 space-x-reverse">
        <button 
          @click="exportToCSV"
          class="btn-secondary px-4 py-2"
        >
          <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          تصدير CSV
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <AdminStatCard
        title="إجمالي المعاملات"
        :value="stats.totalTransactions"
        icon="orders"
        icon-color="blue"
        :loading="loading"
      />
      
      <AdminStatCard
        title="إجمالي الإيداعات"
        :value="stats.totalDeposits"
        unit="USD"
        icon="revenue"
        icon-color="green"
        :loading="loading"
      />
      
      <AdminStatCard
        title="إجمالي السحوبات"
        :value="stats.totalWithdrawals"
        unit="USD"
        icon="revenue"
        icon-color="red"
        :loading="loading"
      />
      
      <AdminStatCard
        title="المعاملات المعلقة"
        :value="stats.pendingTransactions"
        icon="pending"
        icon-color="yellow"
        :loading="loading"
      />
    </div>

    <!-- Currency Breakdown -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Net Balance -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <h3 class="text-lg font-semibold text-white-force mb-4">الرصيد الصافي</h3>
        <div class="text-center">
          <div class="text-3xl font-bold text-white-force mb-2">
            ${{ stats.netBalance.toFixed(2) }}
          </div>
          <div class="text-sm text-theme-text-muted">
            الإيداعات - السحوبات
          </div>
        </div>
      </div>

      <!-- Currency Breakdown -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <h3 class="text-lg font-semibold text-white-force mb-4">توزيع العملات</h3>
        <div class="space-y-3">
          <div v-for="(data, currency) in currencyBreakdown" :key="currency" class="flex items-center justify-between">
            <span class="text-theme-text-secondary">{{ currency }}</span>
            <div class="text-right">
              <div class="text-sm text-white-force">
                {{ data.count }} معاملة
              </div>
              <div class="text-xs text-theme-text-muted">
                ${{ (data.deposits - data.withdrawals).toFixed(2) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Transactions Table -->
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
      <h3 class="text-lg font-semibold text-white-force mb-4">قائمة المعاملات</h3>
      
      <!-- Filters -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <!-- Search -->
        <div>
          <input
            v-model="filters.search"
            type="text"
            placeholder="البحث برقم المعاملة أو اسم المستخدم"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          />
        </div>
        
        <!-- Type Filter -->
        <div>
          <select
            v-model="filters.type"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          >
            <option value="">جميع الأنواع</option>
            <option value="deposit">إيداع</option>
            <option value="withdrawal">سحب</option>
          </select>
        </div>
        
        <!-- Currency Filter -->
        <div>
          <select
            v-model="filters.currency"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          >
            <option value="">جميع العملات</option>
            <option value="USD">USD</option>
            <option value="SAR">SAR</option>
            <option value="AED">AED</option>
          </select>
        </div>
        
        <!-- Status Filter -->
        <div>
          <select
            v-model="filters.status"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          >
            <option value="">جميع الحالات</option>
            <option value="completed">مكتمل</option>
            <option value="pending">معلق</option>
            <option value="declined">مرفوض</option>
          </select>
        </div>
        
        <!-- Date From -->
        <div>
          <input
            v-model="filters.dateFrom"
            type="date"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          />
        </div>
        
        <!-- Clear Filters -->
        <div>
          <button
            @click="clearFilters"
            class="w-full btn-secondary py-2"
          >
            مسح الفلاتر
          </button>
        </div>
      </div>

      <!-- Transactions List -->
      <div class="space-y-4">
        <div 
          v-for="transaction in filteredTransactions" 
          :key="transaction.id"
          class="bg-theme-surface/30 rounded-2xl p-4 hover:bg-theme-surface/50 transition-colors"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-4 space-x-reverse">
              <div 
                class="w-10 h-10 rounded-full flex items-center justify-center"
                :class="getTypeIconClass(transaction.type)"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path v-if="transaction.type === 'deposit'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-white-force">{{ transaction.id }}</h4>
                <p class="text-sm text-theme-text-muted">{{ transaction.userName }}</p>
                <p class="text-xs text-theme-text-muted">{{ transaction.userEmail }}</p>
              </div>
            </div>
            <span 
              class="px-3 py-1 rounded-full text-xs font-medium"
              :class="getStatusClass(transaction.status)"
            >
              {{ getStatusText(transaction.status) }}
            </span>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
            <div>
              <span class="text-theme-text-muted">النوع:</span>
              <div class="text-white-force font-medium">{{ getTypeText(transaction.type) }}</div>
            </div>
            <div>
              <span class="text-theme-text-muted">المبلغ:</span>
              <div class="text-white-force font-medium">
                {{ transaction.amount }} {{ transaction.paidCurrency }}
              </div>
              <div class="text-xs text-theme-primary">
                ≈ ${{ transaction.usdEquivalent }} USD
              </div>
            </div>
            <div>
              <span class="text-theme-text-muted">الطريقة:</span>
              <div class="text-white-force font-medium">{{ getMethodText(transaction.method) }}</div>
            </div>
            <div>
              <span class="text-theme-text-muted">التاريخ:</span>
              <div class="text-white-force font-medium">{{ formatDate(transaction.createdAt) }}</div>
            </div>
          </div>
          
          <div v-if="transaction.reference" class="mb-4">
            <span class="text-theme-text-muted text-sm">المرجع:</span>
            <p class="text-theme-text-secondary text-sm mt-1 font-mono">{{ transaction.reference }}</p>
          </div>
          
          <div v-if="transaction.notes" class="mb-4">
            <span class="text-theme-text-muted text-sm">ملاحظات:</span>
            <p class="text-theme-text-secondary text-sm mt-1">{{ transaction.notes }}</p>
          </div>
          
          <div class="flex justify-end space-x-2 space-x-reverse">
            <button
              v-if="transaction.status === 'pending'"
              @click="approveTransaction(transaction)"
              class="btn-primary text-xs px-3 py-1"
            >
              موافقة
            </button>
            <button
              v-if="transaction.status === 'pending'"
              @click="declineTransaction(transaction)"
              class="btn-secondary text-xs px-3 py-1"
            >
              رفض
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'admin'
})

// Composables
const { 
  filteredTransactions, 
  stats, 
  currencyBreakdown,
  loading, 
  filters, 
  updateTransactionStatus,
  setFilters,
  clearFilters,
  exportToCSV
} = useAdminWallet()

// Methods
const approveTransaction = async (transaction: any) => {
  try {
    await updateTransactionStatus(transaction.id, 'completed', 'تمت الموافقة على المعاملة')
  } catch (error) {
    console.error('Failed to approve transaction:', error)
  }
}

const declineTransaction = async (transaction: any) => {
  try {
    await updateTransactionStatus(transaction.id, 'declined', 'تم رفض المعاملة')
  } catch (error) {
    console.error('Failed to decline transaction:', error)
  }
}

// Helper functions
const getTypeIconClass = (type: string) => {
  return type === 'deposit' 
    ? 'bg-green-500/20 text-green-400' 
    : 'bg-red-500/20 text-red-400'
}

const getStatusClass = (status: string) => {
  const classes = {
    completed: 'bg-green-500/20 text-green-400 border border-green-500/30',
    pending: 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30',
    declined: 'bg-red-500/20 text-red-400 border border-red-500/30'
  }
  return classes[status as keyof typeof classes] || classes.pending
}

const getStatusText = (status: string) => {
  const texts = {
    completed: 'مكتمل',
    pending: 'معلق',
    declined: 'مرفوض'
  }
  return texts[status as keyof typeof texts] || status
}

const getTypeText = (type: string) => {
  const texts = {
    deposit: 'إيداع',
    withdrawal: 'سحب'
  }
  return texts[type as keyof typeof texts] || type
}

const getMethodText = (method: string) => {
  const texts = {
    bank_transfer: 'تحويل بنكي',
    credit_card: 'بطاقة ائتمانية',
    paypal: 'PayPal'
  }
  return texts[method as keyof typeof texts] || method
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Watch filters
watch(filters, (newFilters) => {
  setFilters(newFilters)
}, { deep: true })
</script>
