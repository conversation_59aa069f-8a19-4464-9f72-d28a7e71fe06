export const useAdminCurrencies = () => {
  const currencies = ref([
    {
      id: 1,
      code: 'USD',
      name: 'دولار أمريكي',
      symbol: '$',
      exchangeRate: 1.0,
      status: 'active',
      isBase: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      code: 'SAR',
      name: 'ريال سعودي',
      symbol: 'ر.س',
      exchangeRate: 3.75,
      status: 'active',
      isBase: false,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-07-25T14:20:00Z'
    },
    {
      id: 3,
      code: 'AED',
      name: 'درهم إماراتي',
      symbol: 'د.إ',
      exchangeRate: 3.67,
      status: 'active',
      isBase: false,
      createdAt: '2024-02-01T09:15:00Z',
      updatedAt: '2024-07-20T11:45:00Z'
    },
    {
      id: 4,
      code: 'EUR',
      name: 'يورو',
      symbol: '€',
      exchangeRate: 0.85,
      status: 'inactive',
      isBase: false,
      createdAt: '2024-03-10T16:20:00Z',
      updatedAt: '2024-06-15T13:30:00Z'
    },
    {
      id: 5,
      code: 'GBP',
      name: 'جنيه إسترليني',
      symbol: '£',
      exchangeRate: 0.73,
      status: 'active',
      isBase: false,
      createdAt: '2024-04-05T12:10:00Z',
      updatedAt: '2024-07-28T09:25:00Z'
    }
  ])

  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed active currencies
  const activeCurrencies = computed(() => 
    currencies.value.filter(currency => currency.status === 'active')
  )

  // Computed non-base currencies (can be edited/deleted)
  const editableCurrencies = computed(() => 
    currencies.value.filter(currency => !currency.isBase)
  )

  // Stats
  const stats = computed(() => ({
    totalCurrencies: currencies.value.length,
    activeCurrencies: currencies.value.filter(c => c.status === 'active').length,
    inactiveCurrencies: currencies.value.filter(c => c.status === 'inactive').length
  }))

  // Methods
  const getCurrencyById = (id: number) => {
    return currencies.value.find(currency => currency.id === id)
  }

  const getCurrencyByCode = (code: string) => {
    return currencies.value.find(currency => currency.code === code)
  }

  const addCurrency = async (currencyData: {
    code: string
    name: string
    symbol: string
    exchangeRate: number
  }) => {
    loading.value = true
    try {
      // Validate currency code doesn't exist
      if (currencies.value.some(c => c.code === currencyData.code)) {
        throw new Error('رمز العملة موجود بالفعل')
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newCurrency = {
        id: Math.max(...currencies.value.map(c => c.id)) + 1,
        ...currencyData,
        status: 'active' as const,
        isBase: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      currencies.value.push(newCurrency)
      return newCurrency
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'فشل في إضافة العملة'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateCurrency = async (id: number, updates: {
    name?: string
    symbol?: string
    exchangeRate?: number
    status?: 'active' | 'inactive'
  }) => {
    loading.value = true
    try {
      const currency = currencies.value.find(c => c.id === id)
      if (!currency) {
        throw new Error('العملة غير موجودة')
      }

      if (currency.isBase) {
        throw new Error('لا يمكن تعديل العملة الأساسية')
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      Object.assign(currency, updates, {
        updatedAt: new Date().toISOString()
      })
      
      return currency
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'فشل في تحديث العملة'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteCurrency = async (id: number) => {
    loading.value = true
    try {
      const currency = currencies.value.find(c => c.id === id)
      if (!currency) {
        throw new Error('العملة غير موجودة')
      }

      if (currency.isBase) {
        throw new Error('لا يمكن حذف العملة الأساسية')
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const index = currencies.value.findIndex(c => c.id === id)
      currencies.value.splice(index, 1)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'فشل في حذف العملة'
      throw err
    } finally {
      loading.value = false
    }
  }

  const toggleCurrencyStatus = async (id: number) => {
    const currency = currencies.value.find(c => c.id === id)
    if (!currency || currency.isBase) return

    const newStatus = currency.status === 'active' ? 'inactive' : 'active'
    await updateCurrency(id, { status: newStatus })
  }

  // Convert amount between currencies
  const convertAmount = (amount: number, fromCurrency: string, toCurrency: string) => {
    const fromCurr = getCurrencyByCode(fromCurrency)
    const toCurr = getCurrencyByCode(toCurrency)
    
    if (!fromCurr || !toCurr) {
      throw new Error('عملة غير صحيحة')
    }

    // Convert to USD first, then to target currency
    const usdAmount = fromCurr.isBase ? amount : amount / fromCurr.exchangeRate
    const targetAmount = toCurr.isBase ? usdAmount : usdAmount * toCurr.exchangeRate
    
    return Math.round(targetAmount * 100) / 100
  }

  // Get exchange rate between two currencies
  const getExchangeRate = (fromCurrency: string, toCurrency: string) => {
    const fromCurr = getCurrencyByCode(fromCurrency)
    const toCurr = getCurrencyByCode(toCurrency)
    
    if (!fromCurr || !toCurr) {
      return 1
    }

    if (fromCurrency === toCurrency) {
      return 1
    }

    // Convert via USD
    const usdRate = fromCurr.isBase ? 1 : 1 / fromCurr.exchangeRate
    const targetRate = toCurr.isBase ? 1 : toCurr.exchangeRate
    
    return Math.round(usdRate * targetRate * 10000) / 10000
  }

  // Format amount with currency symbol
  const formatAmount = (amount: number, currencyCode: string) => {
    const currency = getCurrencyByCode(currencyCode)
    if (!currency) return amount.toString()

    const formattedAmount = new Intl.NumberFormat('ar-SA', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)

    return `${formattedAmount} ${currency.symbol}`
  }

  // Validate currency data
  const validateCurrencyData = (data: {
    code: string
    name: string
    symbol: string
    exchangeRate: number
  }) => {
    const errors: string[] = []

    if (!data.code || data.code.length !== 3) {
      errors.push('رمز العملة يجب أن يكون 3 أحرف')
    }

    if (!data.name || data.name.trim().length < 2) {
      errors.push('اسم العملة مطلوب')
    }

    if (!data.symbol || data.symbol.trim().length === 0) {
      errors.push('رمز العملة مطلوب')
    }

    if (!data.exchangeRate || data.exchangeRate <= 0) {
      errors.push('سعر الصرف يجب أن يكون أكبر من صفر')
    }

    return errors
  }

  const clearError = () => {
    error.value = null
  }

  return {
    currencies: readonly(currencies),
    activeCurrencies,
    editableCurrencies,
    stats,
    loading: readonly(loading),
    error: readonly(error),
    getCurrencyById,
    getCurrencyByCode,
    addCurrency,
    updateCurrency,
    deleteCurrency,
    toggleCurrencyStatus,
    convertAmount,
    getExchangeRate,
    formatAmount,
    validateCurrencyData,
    clearError
  }
}
