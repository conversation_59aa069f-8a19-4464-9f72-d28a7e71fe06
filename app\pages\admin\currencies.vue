<template>
  <div class="space-y-6" dir="rtl">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-white-force">إدارة العملات</h1>
        <p class="text-theme-text-muted">إدارة العملات المدعومة وأسعار الصرف</p>
      </div>
      
      <div class="flex items-center space-x-3 space-x-reverse">
        <button 
          @click="showAddModal = true"
          class="btn-primary px-4 py-2"
        >
          <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          إضافة عملة
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      <AdminStatCard
        title="إجمالي العملات"
        :value="stats.totalCurrencies"
        icon="revenue"
        icon-color="blue"
        :loading="loading"
      />
      
      <AdminStatCard
        title="العملات النشطة"
        :value="stats.activeCurrencies"
        icon="revenue"
        icon-color="green"
        :loading="loading"
      />
      
      <AdminStatCard
        title="العملات غير النشطة"
        :value="stats.inactiveCurrencies"
        icon="revenue"
        icon-color="red"
        :loading="loading"
      />
    </div>

    <!-- Base Currency Info -->
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
      <h3 class="text-lg font-semibold text-white-force mb-4">العملة الأساسية</h3>
      <div class="bg-blue-500/10 border border-blue-500/20 rounded-2xl p-4">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-semibold text-white-force">دولار أمريكي (USD)</h4>
            <p class="text-theme-text-muted">العملة الأساسية للنظام - لا يمكن تعديلها أو حذفها</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Currencies Table -->
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
      <h3 class="text-lg font-semibold text-white-force mb-4">قائمة العملات</h3>
      
      <!-- Currencies Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div 
          v-for="currency in editableCurrencies" 
          :key="currency.id"
          class="bg-theme-surface/30 rounded-2xl p-4 hover:bg-theme-surface/50 transition-colors"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div class="w-10 h-10 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                <span class="text-white font-bold text-sm">{{ currency.code }}</span>
              </div>
              <div>
                <h4 class="font-medium text-white-force">{{ currency.name }}</h4>
                <p class="text-sm text-theme-text-muted">{{ currency.symbol }}</p>
              </div>
            </div>
            <span 
              class="px-2 py-1 rounded-full text-xs font-medium"
              :class="getStatusClass(currency.status)"
            >
              {{ getStatusText(currency.status) }}
            </span>
          </div>
          
          <div class="space-y-2 text-sm mb-4">
            <div class="flex justify-between">
              <span class="text-theme-text-muted">سعر الصرف:</span>
              <span class="text-white-force font-medium">{{ currency.exchangeRate }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-theme-text-muted">مقابل USD:</span>
              <span class="text-theme-text-secondary">1 USD = {{ currency.exchangeRate }} {{ currency.code }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-theme-text-muted">آخر تحديث:</span>
              <span class="text-theme-text-secondary">{{ formatDate(currency.updatedAt) }}</span>
            </div>
          </div>
          
          <!-- Conversion Examples -->
          <div class="bg-theme-primary/10 border border-theme-primary/20 rounded-lg p-3 mb-4">
            <h5 class="text-xs font-medium text-theme-primary mb-2">أمثلة التحويل</h5>
            <div class="text-xs text-theme-text-secondary space-y-1">
              <div>$10 = {{ (10 * currency.exchangeRate).toFixed(2) }} {{ currency.code }}</div>
              <div>$50 = {{ (50 * currency.exchangeRate).toFixed(2) }} {{ currency.code }}</div>
              <div>$100 = {{ (100 * currency.exchangeRate).toFixed(2) }} {{ currency.code }}</div>
            </div>
          </div>
          
          <div class="flex justify-end space-x-2 space-x-reverse">
            <button
              @click="editCurrency(currency)"
              class="btn-secondary text-xs px-3 py-1"
            >
              تعديل
            </button>
            <button
              @click="toggleCurrencyStatus(currency)"
              :class="currency.status === 'active' ? 'btn-secondary' : 'btn-primary'"
              class="text-xs px-3 py-1"
            >
              {{ currency.status === 'active' ? 'إلغاء تفعيل' : 'تفعيل' }}
            </button>
            <button
              @click="deleteCurrency(currency)"
              class="text-red-400 hover:text-red-300 text-xs px-2"
              title="حذف"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Currency Form Modal -->
    <AdminCurrencyForm
      :show="showAddModal || showEditModal"
      :currency="selectedCurrency"
      @close="closeModal"
      @save="handleSave"
      @update="handleUpdate"
    />
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'admin'
})

// Composables
const { 
  currencies,
  editableCurrencies,
  stats,
  loading,
  error,
  addCurrency,
  updateCurrency,
  deleteCurrency: deleteCurrencyAction,
  toggleCurrencyStatus: toggleStatus,
  clearError
} = useAdminCurrencies()

// State
const showAddModal = ref(false)
const showEditModal = ref(false)
const selectedCurrency = ref(null)

// Methods
const editCurrency = (currency: any) => {
  selectedCurrency.value = currency
  showEditModal.value = true
}

const toggleCurrencyStatus = async (currency: any) => {
  try {
    await toggleStatus(currency.id)
  } catch (error) {
    console.error('Failed to toggle currency status:', error)
  }
}

const deleteCurrency = async (currency: any) => {
  if (confirm(`هل أنت متأكد من حذف عملة ${currency.name}؟`)) {
    try {
      await deleteCurrencyAction(currency.id)
    } catch (error) {
      console.error('Failed to delete currency:', error)
    }
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  selectedCurrency.value = null
  clearError()
}

const handleSave = async (currencyData: any) => {
  try {
    await addCurrency(currencyData)
    closeModal()
  } catch (error) {
    console.error('Failed to add currency:', error)
  }
}

const handleUpdate = async (id: number, currencyData: any) => {
  try {
    await updateCurrency(id, currencyData)
    closeModal()
  } catch (error) {
    console.error('Failed to update currency:', error)
  }
}

// Helper functions
const getStatusClass = (status: string) => {
  const classes = {
    active: 'bg-green-500/20 text-green-400 border border-green-500/30',
    inactive: 'bg-red-500/20 text-red-400 border border-red-500/30'
  }
  return classes[status as keyof typeof classes] || classes.active
}

const getStatusText = (status: string) => {
  const texts = {
    active: 'نشط',
    inactive: 'غير نشط'
  }
  return texts[status as keyof typeof texts] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
