<template>
  <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl sm:rounded-3xl p-4 sm:p-6 hover:border-theme-primary/30 hover:shadow-lg transition-all duration-300 group">
    <div class="flex items-center justify-between">
      <div class="flex-1 min-w-0">
        <div class="flex items-center space-x-2 sm:space-x-3 space-x-reverse mb-2">
          <div
            class="w-10 h-10 sm:w-12 sm:h-12 rounded-xl sm:rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
            :class="iconBgClass"
          >
            <component
              :is="iconComponent"
              class="w-5 h-5 sm:w-6 sm:h-6"
              :class="iconClass"
            />
          </div>

          <div class="flex-1 min-w-0">
            <h3 class="text-xs sm:text-sm font-medium text-theme-text-muted truncate">{{ title }}</h3>
            <div class="flex items-baseline space-x-1 sm:space-x-2 space-x-reverse">
              <p class="text-lg sm:text-2xl font-bold text-white-force">{{ formattedValue }}</p>
              <span v-if="unit" class="text-xs sm:text-sm text-theme-text-secondary">{{ unit }}</span>
            </div>
          </div>
        </div>
        
        <!-- Change Indicator -->
        <div v-if="change !== undefined" class="flex items-center space-x-1 sm:space-x-2 space-x-reverse">
          <div
            class="flex items-center space-x-1 space-x-reverse text-xs font-medium px-2 py-1 rounded-full"
            :class="changeClass"
          >
            <component :is="changeIcon" class="w-3 h-3" />
            <span>{{ Math.abs(change) }}%</span>
          </div>
          <span class="text-xs text-theme-text-muted truncate">{{ changeLabel || 'من الشهر الماضي' }}</span>
        </div>

        <!-- Description -->
        <p v-if="description" class="text-xs text-theme-text-muted mt-2 truncate">{{ description }}</p>
      </div>
      
      <!-- Loading State -->
      <div v-if="loading" class="absolute inset-0 bg-theme-surface/50 backdrop-blur-sm rounded-3xl flex items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-theme-primary"></div>
      </div>
    </div>
    
    <!-- Click Action -->
    <div v-if="clickable" class="mt-4 pt-4 border-t border-theme-light">
      <button 
        @click="$emit('click')"
        class="text-sm text-theme-primary hover:text-theme-primary-hover transition-colors flex items-center space-x-2 space-x-reverse"
      >
        <span>{{ actionText || 'عرض التفاصيل' }}</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  UsersIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  MinusIcon
} from '@heroicons/vue/24/outline'

interface Props {
  title: string
  value: number | string
  unit?: string
  change?: number
  changeLabel?: string
  description?: string
  icon?: string
  iconColor?: 'blue' | 'green' | 'purple' | 'red' | 'yellow' | 'gray'
  loading?: boolean
  clickable?: boolean
  actionText?: string
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: 'blue',
  loading: false,
  clickable: false
})

const emit = defineEmits<{
  click: []
}>()

// Icon mapping
const iconMap = {
  users: UsersIcon,
  orders: ShoppingBagIcon,
  revenue: CurrencyDollarIcon,
  pending: ClockIcon
}

const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || UsersIcon
})

// Icon styling
const iconBgClass = computed(() => {
  const colorMap = {
    blue: 'bg-blue-500/20',
    green: 'bg-green-500/20',
    purple: 'bg-purple-500/20',
    red: 'bg-red-500/20',
    yellow: 'bg-yellow-500/20',
    gray: 'bg-gray-500/20'
  }
  return colorMap[props.iconColor]
})

const iconClass = computed(() => {
  const colorMap = {
    blue: 'text-blue-400',
    green: 'text-green-400',
    purple: 'text-purple-400',
    red: 'text-red-400',
    yellow: 'text-yellow-400',
    gray: 'text-gray-400'
  }
  return colorMap[props.iconColor]
})

// Change indicator
const changeIcon = computed(() => {
  if (props.change === undefined) return MinusIcon
  if (props.change > 0) return ArrowTrendingUpIcon
  if (props.change < 0) return ArrowTrendingDownIcon
  return MinusIcon
})

const changeClass = computed(() => {
  if (props.change === undefined) return 'bg-gray-500/20 text-gray-400'
  if (props.change > 0) return 'bg-green-500/20 text-green-400'
  if (props.change < 0) return 'bg-red-500/20 text-red-400'
  return 'bg-gray-500/20 text-gray-400'
})

// Format value
const formattedValue = computed(() => {
  if (typeof props.value === 'string') return props.value
  
  // Format numbers with Arabic locale
  return new Intl.NumberFormat('ar-SA', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(props.value)
})
</script>
