<template>
  <div class="space-y-4 sm:space-y-6" dir="rtl">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold text-white-force">إدارة المستخدمين والعمال</h1>
        <p class="text-sm sm:text-base text-theme-text-muted">إدارة حسابات المستخدمين وأدوارهم وأرصدة المحافظ</p>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-5 xl:gap-6">
      <!-- Total Users -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl sm:rounded-3xl p-4 sm:p-6 hover:border-theme-primary/30 transition-all duration-300">
        <div class="flex items-center justify-between">
          <div class="min-w-0 flex-1">
            <p class="text-theme-text-muted text-xs sm:text-sm truncate">إجمالي المستخدمين</p>
            <p class="text-lg sm:text-2xl font-bold text-white-force">{{ stats.totalUsers }}</p>
          </div>
          <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-500/20 rounded-xl sm:rounded-2xl flex items-center justify-center ml-2">
            <svg class="w-5 h-5 sm:w-6 sm:h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Active Users -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-theme-text-muted text-sm">المستخدمون النشطون</p>
            <p class="text-2xl font-bold text-white-force">{{ stats.activeUsers }}</p>
          </div>
          <div class="w-12 h-12 bg-green-500/20 rounded-2xl flex items-center justify-center">
            <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Workers -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-theme-text-muted text-sm">العمال</p>
            <p class="text-2xl font-bold text-white-force">{{ stats.workers }}</p>
          </div>
          <div class="w-12 h-12 bg-purple-500/20 rounded-2xl flex items-center justify-center">
            <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V8m8 0V6a2 2 0 00-2-2H10a2 2 0 00-2 2v2"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Total Wallet Balance -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-theme-text-muted text-sm">إجمالي أرصدة المحافظ</p>
            <p class="text-2xl font-bold text-white-force">${{ stats.totalWalletBalance.toLocaleString() }}</p>
          </div>
          <div class="w-12 h-12 bg-yellow-500/20 rounded-2xl flex items-center justify-center">
            <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
      <h3 class="text-lg font-semibold text-white-force mb-4">فلاتر البحث</h3>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <input
            v-model="filters.search"
            type="text"
            placeholder="البحث بالاسم أو البريد الإلكتروني"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          />
        </div>
        
        <!-- Role Filter -->
        <div>
          <select
            v-model="filters.role"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          >
            <option value="">جميع الأدوار</option>
            <option value="admin">مدير</option>
            <option value="worker">عامل</option>
            <option value="user">مستخدم</option>
          </select>
        </div>
        
        <!-- Status Filter -->
        <div>
          <select
            v-model="filters.status"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          >
            <option value="">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="suspended">معلق</option>
          </select>
        </div>
        
        <!-- Clear Filters -->
        <div>
          <button
            @click="clearFilters"
            class="w-full btn-secondary py-2"
          >
            مسح الفلاتر
          </button>
        </div>
      </div>
    </div>



    <!-- Users Table -->
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
      <h3 class="text-lg font-semibold text-white-force mb-4">قائمة المستخدمين</h3>
      


      <!-- Loading State -->
      <div v-if="loading" class="text-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-theme-primary mx-auto"></div>
        <p class="text-theme-text-muted mt-2">جاري التحميل...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="!filteredUsers || filteredUsers.length === 0" class="text-center py-8">
        <svg class="w-16 h-16 text-theme-text-muted mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m0 0V9a2 2 0 012-2h2m0 0V6a2 2 0 012-2h2.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V9a2 2 0 012 2v2m0 0h2m-6 0a2 2 0 002 2v1a2 2 0 01-2 2H9a2 2 0 01-2-2v-1a2 2 0 012-2h2z"></path>
        </svg>
        <p class="text-theme-text-muted">لا يوجد مستخدمون</p>

      </div>



      <!-- Desktop Table -->
      <div class="hidden md:block overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-theme-light">
              <th class="text-right py-3 px-4 text-sm font-medium text-theme-text-muted">المستخدم</th>
              <th class="text-right py-3 px-4 text-sm font-medium text-theme-text-muted">الدور</th>
              <th class="text-right py-3 px-4 text-sm font-medium text-theme-text-muted">الحالة</th>
              <th class="text-right py-3 px-4 text-sm font-medium text-theme-text-muted">رصيد USD</th>
              <th class="text-right py-3 px-4 text-sm font-medium text-theme-text-muted">تاريخ الانضمام</th>
              <th class="text-right py-3 px-4 text-sm font-medium text-theme-text-muted">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="user in filteredUsers"
              :key="user.id"
              class="border-b border-theme-light/30 hover:bg-theme-surface/20 transition-colors"
            >
              <td class="py-4 px-4">
                <div class="flex items-center space-x-3 space-x-reverse">
                  <div class="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium text-white-force">{{ user.name }}</div>
                    <div class="text-sm text-theme-text-muted">{{ user.email }}</div>
                  </div>
                </div>
              </td>
              <td class="py-4 px-4">
                <span 
                  class="px-2 py-1 rounded-full text-xs font-medium"
                  :class="getRoleClass(user.role)"
                >
                  {{ getRoleText(user.role) }}
                </span>
              </td>
              <td class="py-4 px-4">
                <span 
                  class="px-2 py-1 rounded-full text-xs font-medium"
                  :class="getStatusClass(user.status)"
                >
                  {{ getStatusText(user.status) }}
                </span>
              </td>
              <td class="py-4 px-4">
                <span class="text-white-force">${{ user.walletBalances?.USD || 0 }}</span>
              </td>
              <td class="py-4 px-4">
                <span class="text-theme-text-secondary">{{ formatDate(user.joinedAt) }}</span>
              </td>
              <td class="py-4 px-4">
                <div class="flex items-center space-x-2 space-x-reverse">
                  <button
                    @click="viewUser(user)"
                    class="p-2 text-theme-primary hover:text-theme-primary-hover hover:bg-theme-surface/20 rounded-lg transition-colors"
                    title="عرض التفاصيل"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  </button>
                  <button
                    @click="editUser(user)"
                    class="p-2 text-blue-400 hover:text-blue-300 hover:bg-theme-surface/20 rounded-lg transition-colors"
                    title="تعديل"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button
                    @click="toggleUserStatus(user)"
                    :class="[
                      'p-2 rounded-lg transition-colors hover:bg-theme-surface/20',
                      user.status === 'active' ? 'text-red-400 hover:text-red-300' : 'text-green-400 hover:text-green-300'
                    ]"
                    :title="user.status === 'active' ? 'تعليق' : 'تفعيل'"
                  >
                    <svg v-if="user.status === 'active'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                    </svg>
                    <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Mobile Cards -->
      <div class="md:hidden space-y-4">
        <div 
          v-for="user in filteredUsers" 
          :key="user.id"
          class="bg-theme-surface/30 rounded-2xl p-4"
        >
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3 space-x-reverse">
                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <div>
                  <h4 class="font-medium text-white-force">{{ user.name }}</h4>
                  <p class="text-sm text-theme-text-muted">{{ user.email }}</p>
                </div>
              </div>
              <span 
                class="px-2 py-1 rounded-full text-xs font-medium"
                :class="getRoleClass(user.role)"
              >
                {{ getRoleText(user.role) }}
              </span>
            </div>
            
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-theme-text-muted">الحالة:</span>
                <span 
                  class="mr-2 px-2 py-1 rounded-full text-xs font-medium"
                  :class="getStatusClass(user.status)"
                >
                  {{ getStatusText(user.status) }}
                </span>
              </div>
              <div>
                <span class="text-theme-text-muted">رصيد USD:</span>
                <span class="text-white-force font-medium mr-2">${{ user.walletBalances?.USD || 0 }}</span>
              </div>
            </div>
            
            <div class="flex justify-end space-x-2 space-x-reverse pt-2 border-t border-theme-light/30">
              <button
                @click="viewUser(user)"
                class="btn-secondary text-xs px-3 py-1"
              >
                عرض
              </button>
              <button
                @click="editUser(user)"
                class="btn-primary text-xs px-3 py-1"
              >
                تعديل
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Detail Modal -->
    <AdminUserModal
      :show="showUserModal"
      :user="selectedUser"
      :initial-mode="modalMode"
      @close="closeModal"
      @user-updated="handleUserUpdated"
      @status-toggled="handleStatusToggled"
    />
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'admin'
})

// Composables
const {
  users,
  loading,
  stats,
  filters,
  filteredUsers,
  updateUserRole,
  updateUserStatus,
  updateWalletBalance,
  clearFilters
} = useAdminUsers()



// State
const showUserModal = ref(false)
const selectedUser = ref(null)
const modalMode = ref<'view' | 'edit'>('view')

// Methods
const viewUser = (user: any) => {
  selectedUser.value = user
  modalMode.value = 'view'
  showUserModal.value = true
}

const editUser = (user: any) => {
  selectedUser.value = user
  modalMode.value = 'edit'
  showUserModal.value = true
}

const closeModal = () => {
  showUserModal.value = false
  selectedUser.value = null
  modalMode.value = 'view'
}

const handleUserUpdated = async (updatedUser: any) => {
  try {
    // Update user role if changed
    if (updatedUser.role !== selectedUser.value?.role) {
      await updateUserRole(updatedUser.id, updatedUser.role)
    }

    // Update user status if changed
    if (updatedUser.status !== selectedUser.value?.status) {
      await updateUserStatus(updatedUser.id, updatedUser.status)
    }

    // Update wallet balances if changed
    const originalBalances = selectedUser.value?.walletBalances || {}
    const newBalances = updatedUser.walletBalances || {}

    for (const currency in newBalances) {
      if (originalBalances[currency] !== newBalances[currency]) {
        await updateWalletBalance(updatedUser.id, currency, newBalances[currency])
      }
    }

    // Update the selected user reference
    selectedUser.value = updatedUser
  } catch (error) {
    console.error('Failed to update user:', error)
  }
}

const handleStatusToggled = async (userId: number, newStatus: string) => {
  try {
    await updateUserStatus(userId, newStatus)
    if (selectedUser.value && selectedUser.value.id === userId) {
      selectedUser.value.status = newStatus
    }
  } catch (error) {
    console.error('Failed to toggle user status:', error)
  }
}

const toggleUserStatus = async (user: any) => {
  const newStatus = user.status === 'active' ? 'suspended' : 'active'
  const confirmMessage = newStatus === 'suspended'
    ? 'هل أنت متأكد من تعليق هذا المستخدم؟'
    : 'هل أنت متأكد من تفعيل هذا المستخدم؟'

  if (confirm(confirmMessage)) {
    await handleStatusToggled(user.id, newStatus)
  }
}

// Helper functions
const formatDate = (dateString?: string) => {
  if (!dateString) return 'غير محدد'
  return new Date(dateString).toLocaleDateString('ar-SA')
}

const getRoleClass = (role: string) => {
  const classes = {
    admin: 'bg-red-500/20 text-red-400',
    worker: 'bg-blue-500/20 text-blue-400',
    user: 'bg-green-500/20 text-green-400'
  }
  return classes[role as keyof typeof classes] || 'bg-gray-500/20 text-gray-400'
}

const getRoleText = (role: string) => {
  const texts = {
    admin: 'مدير',
    worker: 'عامل',
    user: 'مستخدم'
  }
  return texts[role as keyof typeof texts] || role
}

const getStatusClass = (status: string) => {
  const classes = {
    active: 'bg-green-500/20 text-green-400',
    suspended: 'bg-red-500/20 text-red-400'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-500/20 text-gray-400'
}

const getStatusText = (status: string) => {
  const texts = {
    active: 'نشط',
    suspended: 'معلق'
  }
  return texts[status as keyof typeof texts] || status
}
</script>
