<template>
  <div class="min-h-screen bg-theme-background" dir="rtl">
    <!-- Mobile Menu Overlay -->
    <div
      v-if="showMobileMenu"
      class="fixed inset-0 z-40 lg:hidden"
      @click="showMobileMenu = false"
    >
      <div class="fixed inset-0 bg-black/60 backdrop-blur-sm"></div>
    </div>

    <!-- Top Navigation -->
    <nav class="bg-theme-glass backdrop-blur-md border-b border-theme-light sticky top-0 z-30 shadow-lg">
      <div class="px-3 sm:px-4 lg:px-6">
        <div class="flex justify-between items-center h-14 sm:h-16">
          <!-- Mobile Menu Button -->
          <button
            @click="showMobileMenu = !showMobileMenu"
            class="lg:hidden p-2 rounded-xl hover:bg-theme-surface/30 transition-all duration-200 active:scale-95"
          >
            <svg class="w-5 h-5 sm:w-6 sm:h-6 text-theme-text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>

          <!-- Logo & Dashboard Title -->
          <div class="flex items-center space-x-2 sm:space-x-4 space-x-reverse">
            <NuxtLink to="/" class="text-lg sm:text-xl font-bold text-white-force hover:text-theme-primary transition-colors">
              بنتاكون
            </NuxtLink>
            <span class="text-theme-text-muted hidden sm:inline">|</span>
            <h1 class="text-sm sm:text-lg font-medium text-theme-text-secondary hidden sm:block">لوحة الإدارة</h1>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-2 sm:space-x-4 space-x-reverse">
            <span class="text-xs sm:text-sm text-theme-text-secondary hidden md:block truncate max-w-24 sm:max-w-none">{{ userDisplayName }}</span>
            <span class="px-2 py-1 text-xs font-medium rounded-full border" :class="roleClass">
              {{ userRole }}
            </span>
            <button @click="handleLogout" class="text-xs sm:text-sm text-theme-text-secondary hover:text-theme-text-primary transition-colors px-2 py-1 rounded-lg hover:bg-theme-surface/20">
              خروج
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Layout -->
    <div class="flex min-h-[calc(100vh-3.5rem)] sm:min-h-[calc(100vh-4rem)]">
      <!-- Sidebar -->
      <aside
        class="fixed inset-y-0 right-0 z-50 w-72 sm:w-80 lg:w-44 xl:w-48 bg-theme-glass backdrop-blur-md border-l border-theme-light transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 shadow-2xl lg:shadow-none"
        :class="showMobileMenu ? 'translate-x-0' : 'translate-x-full'"
      >
        <nav class="p-2 sm:p-3 pt-16 sm:pt-20 lg:pt-3 h-full overflow-y-auto">
          <div class="space-y-1">
            <div class="text-xs font-semibold text-theme-text-muted uppercase tracking-wider mb-3 px-2">
              التنقل
            </div>

            <div class="space-y-1">
              <!-- Dashboard -->
              <NuxtLink
                to="/admin"
                class="admin-nav-link group"
                :class="{ 'admin-nav-active': $route.path === '/admin' }"
                @click="showMobileMenu = false"
              >
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span class="truncate text-xs lg:text-sm">الرئيسية</span>
              </NuxtLink>

              <!-- Users & Workers -->
              <NuxtLink
                to="/admin/users"
                class="admin-nav-link group"
                :class="{ 'admin-nav-active': $route.path.startsWith('/admin/users') }"
                @click="showMobileMenu = false"
              >
                <svg class="w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                <span class="truncate text-xs lg:text-sm">المستخدمون والعمال</span>
              </NuxtLink>

              <!-- Products -->
              <NuxtLink
                to="/admin/products"
                class="admin-nav-link group"
                :class="{ 'admin-nav-active': $route.path.startsWith('/admin/products') }"
                @click="showMobileMenu = false"
              >
                <svg class="w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                <span class="truncate text-xs lg:text-sm">المنتجات</span>
              </NuxtLink>

              <!-- Orders -->
              <NuxtLink
                to="/admin/orders"
                class="admin-nav-link group"
                :class="{ 'admin-nav-active': $route.path.startsWith('/admin/orders') }"
                @click="showMobileMenu = false"
              >
                <svg class="w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                <span class="truncate text-xs lg:text-sm">الطلبات</span>
              </NuxtLink>

              <!-- Wallet Transactions -->
              <NuxtLink 
                to="/admin/wallet" 
                class="admin-nav-link"
                :class="{ 'admin-nav-active': $route.path.startsWith('/admin/wallet') }"
                @click="showMobileMenu = false"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                معاملات المحفظة
              </NuxtLink>

              <!-- Currency Management -->
              <NuxtLink 
                to="/admin/currencies" 
                class="admin-nav-link"
                :class="{ 'admin-nav-active': $route.path.startsWith('/admin/currencies') }"
                @click="showMobileMenu = false"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                إدارة العملات
              </NuxtLink>

              <!-- Settings -->
              <NuxtLink 
                to="/admin/settings" 
                class="admin-nav-link"
                :class="{ 'admin-nav-active': $route.path.startsWith('/admin/settings') }"
                @click="showMobileMenu = false"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                الإعدادات
              </NuxtLink>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 lg:mr-44 xl:mr-48 p-1 sm:p-2 lg:p-2 xl:p-3 pr-0 sm:pr-1 lg:pr-1 xl:pr-1 overflow-hidden">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
const { userDisplayName, userRole, logout } = useAuthStore()
const showMobileMenu = ref(false)

const roleClass = computed(() => {
  const roleClasses = {
    admin: 'bg-red-500/20 text-red-400 border border-red-500/30',
    worker: 'bg-blue-500/20 text-blue-400 border border-blue-500/30', 
    distributor: 'bg-purple-500/20 text-purple-400 border border-purple-500/30',
    user: 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
  }
  return roleClasses[userRole as keyof typeof roleClasses] || 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
})

const handleLogout = async () => {
  await logout()
  await navigateTo('/')
}

// Close mobile menu when route changes
watch(() => useRoute().path, () => {
  showMobileMenu.value = false
})
</script>

<style scoped>
.admin-nav-link {
  @apply flex items-center space-x-2 space-x-reverse px-2 py-2 text-xs lg:text-sm font-medium text-theme-text-secondary rounded-lg hover:bg-theme-surface/30 hover:text-theme-text-primary transition-all duration-200;
}

.admin-nav-link:hover {
  @apply transform scale-[0.98] shadow-lg;
}

.admin-nav-active {
  @apply bg-gradient-to-r from-theme-primary/20 to-theme-secondary/20 text-theme-primary border-r-4 border-theme-primary shadow-lg;
}

.admin-nav-active svg {
  @apply text-theme-primary;
}

/* Mobile optimizations */
@media (max-width: 1024px) {
  .admin-nav-link {
    @apply px-4 py-3 text-base;
  }
}

/* Ensure no gaps - simplified approach */
.admin-content-full-width {
  width: 100%;
  max-width: none;
  margin-right: 0;
  padding-right: 0;
}
</style>
