<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" dir="rtl">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div 
        class="fixed inset-0 transition-opacity bg-black/50 backdrop-blur-sm"
        @click="$emit('close')"
      ></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl shadow-xl">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-bold text-white-force">تفاصيل الطلب</h3>
          <button 
            @click="$emit('close')"
            class="p-2 rounded-lg hover:bg-theme-surface-light transition-colors"
          >
            <svg class="w-5 h-5 text-theme-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div v-if="order" class="space-y-6">
          <!-- Order Info -->
          <div class="bg-theme-surface/30 rounded-2xl p-4">
            <h4 class="text-lg font-semibold text-white-force mb-3">معلومات الطلب</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-theme-text-muted">رقم الطلب:</span>
                <span class="text-white-force font-medium mr-2">{{ order.id }}</span>
              </div>
              <div>
                <span class="text-theme-text-muted">المنتج:</span>
                <span class="text-white-force font-medium mr-2">{{ order.productName }}</span>
              </div>
              <div>
                <span class="text-theme-text-muted">العميل:</span>
                <span class="text-white-force font-medium mr-2">{{ order.userName }}</span>
              </div>
              <div>
                <span class="text-theme-text-muted">تاريخ الطلب:</span>
                <span class="text-white-force font-medium mr-2">{{ formatDate(order.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- Payment Info -->
          <div class="bg-theme-surface/30 rounded-2xl p-4">
            <h4 class="text-lg font-semibold text-white-force mb-3">معلومات الدفع</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-theme-text-muted">العملة المدفوعة:</span>
                <span class="text-white-force font-medium mr-2">{{ order.paidCurrency }}</span>
              </div>
              <div>
                <span class="text-theme-text-muted">المبلغ المدفوع:</span>
                <span class="text-white-force font-medium mr-2">{{ order.paidAmount }}</span>
              </div>
              <div>
                <span class="text-theme-text-muted">سعر الصرف:</span>
                <span class="text-white-force font-medium mr-2">{{ order.exchangeRate }}</span>
              </div>
              <div>
                <span class="text-theme-text-muted">المعادل بالدولار:</span>
                <span class="text-white-force font-medium mr-2">${{ order.usdEquivalent }}</span>
              </div>
            </div>
            
            <!-- Currency Conversion Tooltip -->
            <div class="mt-3 p-3 bg-theme-primary/10 border border-theme-primary/20 rounded-lg">
              <p class="text-xs text-theme-text-secondary">
                <strong>تفاصيل التحويل:</strong>
                {{ order.paidAmount }} {{ order.paidCurrency }} × {{ order.exchangeRate }} = ${{ order.usdEquivalent }} USD
              </p>
            </div>
          </div>

          <!-- Status & Worker -->
          <div class="bg-theme-surface/30 rounded-2xl p-4">
            <h4 class="text-lg font-semibold text-white-force mb-3">الحالة والعامل</h4>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-theme-text-muted">الحالة:</span>
                <span 
                  class="px-3 py-1 rounded-full text-xs font-medium"
                  :class="getStatusClass(order.status)"
                >
                  {{ getStatusText(order.status) }}
                </span>
              </div>
              
              <div v-if="order.assignedWorker" class="flex items-center justify-between">
                <span class="text-theme-text-muted">العامل المعين:</span>
                <span class="text-white-force font-medium">{{ order.assignedWorker }}</span>
              </div>
              
              <div v-if="order.completedAt" class="flex items-center justify-between">
                <span class="text-theme-text-muted">تاريخ الإنجاز:</span>
                <span class="text-white-force font-medium">{{ formatDate(order.completedAt) }}</span>
              </div>
            </div>
          </div>

          <!-- Notes -->
          <div v-if="order.notes" class="bg-theme-surface/30 rounded-2xl p-4">
            <h4 class="text-lg font-semibold text-white-force mb-3">ملاحظات</h4>
            <p class="text-theme-text-secondary">{{ order.notes }}</p>
          </div>

          <!-- Delivery Code -->
          <div v-if="order.deliveryCode" class="bg-green-500/10 border border-green-500/20 rounded-2xl p-4">
            <h4 class="text-lg font-semibold text-green-400 mb-3">كود التسليم</h4>
            <div class="bg-theme-surface/50 rounded-lg p-3 font-mono text-center">
              <span class="text-white-force text-lg font-bold">{{ order.deliveryCode }}</span>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-theme-light">
            <!-- Status Update -->
            <div v-if="order.status !== 'completed'" class="flex-1">
              <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                تحديث الحالة
              </label>
              <select 
                v-model="newStatus"
                class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
              >
                <option value="pending">في الانتظار</option>
                <option value="processing">قيد المعالجة</option>
                <option value="completed">مكتمل</option>
                <option value="declined">مرفوض</option>
              </select>
            </div>

            <!-- Worker Assignment -->
            <div v-if="!order.assignedWorker && order.status === 'pending'" class="flex-1">
              <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                تعيين عامل
              </label>
              <select 
                v-model="selectedWorker"
                class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
              >
                <option value="">اختر عامل</option>
                <option v-for="worker in workers" :key="worker.id" :value="worker.name">
                  {{ worker.name }}
                </option>
              </select>
            </div>

            <!-- Delivery Code Input -->
            <div v-if="order.status === 'processing' && !order.deliveryCode" class="flex-1">
              <label class="block text-sm font-medium text-theme-text-secondary mb-2">
                كود التسليم
              </label>
              <input 
                v-model="deliveryCode"
                type="text"
                placeholder="أدخل كود التسليم"
                class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
              />
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3 space-x-reverse pt-4">
            <button 
              @click="$emit('close')"
              class="btn-secondary px-4 py-2"
            >
              إغلاق
            </button>
            
            <button 
              v-if="hasChanges"
              @click="saveChanges"
              :disabled="loading"
              class="btn-primary px-4 py-2 disabled:opacity-50"
            >
              <span v-if="loading">جاري الحفظ...</span>
              <span v-else>حفظ التغييرات</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
  order: any
  workers: any[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  update: [orderId: string, updates: any]
}>()

const loading = ref(false)
const newStatus = ref('')
const selectedWorker = ref('')
const deliveryCode = ref('')

// Watch for order changes to reset form
watch(() => props.order, (order) => {
  if (order) {
    newStatus.value = order.status
    selectedWorker.value = order.assignedWorker || ''
    deliveryCode.value = order.deliveryCode || ''
  }
}, { immediate: true })

const hasChanges = computed(() => {
  if (!props.order) return false
  
  return newStatus.value !== props.order.status ||
         selectedWorker.value !== (props.order.assignedWorker || '') ||
         deliveryCode.value !== (props.order.deliveryCode || '')
})

const saveChanges = async () => {
  if (!props.order) return
  
  loading.value = true
  try {
    const updates: any = {}
    
    if (newStatus.value !== props.order.status) {
      updates.status = newStatus.value
    }
    
    if (selectedWorker.value && selectedWorker.value !== props.order.assignedWorker) {
      updates.assignedWorker = selectedWorker.value
    }
    
    if (deliveryCode.value && deliveryCode.value !== props.order.deliveryCode) {
      updates.deliveryCode = deliveryCode.value
    }
    
    emit('update', props.order.id, updates)
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30',
    processing: 'bg-blue-500/20 text-blue-400 border border-blue-500/30',
    completed: 'bg-green-500/20 text-green-400 border border-green-500/30',
    declined: 'bg-red-500/20 text-red-400 border border-red-500/30'
  }
  return classes[status as keyof typeof classes] || classes.pending
}

const getStatusText = (status: string) => {
  const texts = {
    pending: 'في الانتظار',
    processing: 'قيد المعالجة',
    completed: 'مكتمل',
    declined: 'مرفوض'
  }
  return texts[status as keyof typeof texts] || status
}
</script>
