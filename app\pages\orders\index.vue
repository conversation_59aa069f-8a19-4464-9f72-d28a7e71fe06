<template>
  <div class="min-h-screen bg-theme-background" dir="rtl">
    <div class="container mx-auto px-4 py-8">
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="section-title text-3xl mb-2">طلباتي</h1>
        <p class="text-theme-secondary">تتبع جميع طلباتك ومشترياتك</p>
      </div>

      <!-- Filter Tabs -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-3 mb-8">
        <div class="flex flex-wrap gap-3">
          <button
            v-for="filter in orderFilters"
            :key="filter.key"
            @click="activeFilter = filter.key"
            :class="[
              'px-5 py-3 rounded-xl text-sm font-semibold transition-all duration-300 hover:scale-105',
              activeFilter === filter.key
                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg shadow-purple-500/25'
                : 'text-theme-secondary hover:text-theme-text-primary hover:bg-theme-surface/40 border border-theme-light/30'
            ]"
          >
            {{ filter.label }}
            <span v-if="filter.count" class="mr-2 px-2 py-0.5 bg-white/20 rounded-full text-xs font-bold">
              {{ filter.count }}
            </span>
          </button>
        </div>
      </div>

      <!-- Orders List -->
      <div class="space-y-8">
        <div v-for="order in filteredOrders" :key="order.id"
             class="group bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-8 hover:border-theme-accent/50 hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300 hover:scale-[1.02]">

          <!-- Order Header -->
          <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-8">
            <div class="flex items-center space-x-4 space-x-reverse mb-4 sm:mb-0">
              <div class="w-14 h-14 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-white-force text-xl font-bold mb-1">طلب #{{ order.id }}</h3>
                <p class="text-theme-muted text-sm">{{ order.date }}</p>
              </div>
            </div>

            <div class="flex items-center space-x-4 space-x-reverse">
              <div :class="[
                'px-4 py-2 rounded-full text-sm font-bold shadow-lg',
                getStatusClass(order.status)
              ]">
                {{ getStatusText(order.status) }}
              </div>
              <div class="text-white-force font-bold text-xl">{{ order.total.toLocaleString() }} ر.س</div>
            </div>
          </div>

          <!-- Order Items -->
          <div class="space-y-4 mb-8">
            <div v-for="item in order.items" :key="item.id"
                 class="flex items-center space-x-5 space-x-reverse p-5 bg-theme-surface/20 rounded-2xl hover:bg-theme-surface/30 transition-all duration-200 border border-theme-light/20">
              <div class="w-20 h-20 bg-theme-surface rounded-xl overflow-hidden flex-shrink-0 shadow-lg">
                <img :src="item.image" :alt="item.name" class="w-full h-full object-cover">
              </div>
              <div class="flex-1 min-w-0">
                <h4 class="text-white-force font-semibold text-lg truncate mb-1">{{ item.name }}</h4>
                <p class="text-theme-muted text-sm mb-2">{{ item.category }}</p>
                <div class="flex items-center space-x-3 space-x-reverse">
                  <span class="text-theme-secondary text-sm font-medium bg-theme-surface/30 px-3 py-1 rounded-lg">الكمية: {{ item.quantity }}</span>
                  <span class="text-theme-accent font-bold text-lg">{{ item.price.toLocaleString() }} ر.س</span>
                </div>
              </div>

              <!-- Item Actions -->
              <div class="flex flex-col space-y-3">
                <button v-if="order.status === 'delivered'"
                        class="btn-secondary px-4 py-2 text-sm font-semibold hover:scale-105 transition-transform duration-200">
                  تقييم المنتج
                </button>
                <button v-if="item.downloadable && order.status === 'delivered'"
                        class="btn-primary px-4 py-2 text-sm font-semibold hover:scale-105 transition-transform duration-200">
                  تحميل فوري
                </button>
              </div>
            </div>
          </div>

          <!-- Order Actions -->
          <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center pt-6 border-t border-theme-light/30">
            <div class="flex items-center space-x-3 space-x-reverse text-sm text-theme-muted mb-4 sm:mb-0 bg-theme-surface/20 px-4 py-2 rounded-xl">
              <svg class="w-5 h-5 text-theme-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span class="font-medium">{{ order.deliveryMethod }}</span>
              <span v-if="order.trackingNumber" class="text-theme-accent font-semibold">
                • رقم التتبع: {{ order.trackingNumber }}
              </span>
            </div>

            <div class="flex space-x-3 space-x-reverse">
              <button v-if="order.status === 'pending'"
                      @click="cancelOrder(order.id)"
                      class="btn-secondary px-5 py-3 text-sm font-semibold hover:scale-105 transition-all duration-200">
                إلغاء الطلب
              </button>
              <button v-if="order.trackingNumber"
                      @click="trackOrder(order.id)"
                      class="btn-primary px-5 py-3 text-sm font-semibold hover:scale-105 transition-all duration-200">
                تتبع الطلب
              </button>
              <NuxtLink :to="`/orders/${order.id}`"
                        class="btn-secondary px-5 py-3 text-sm font-semibold hover:scale-105 transition-all duration-200">
                عرض التفاصيل
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredOrders.length === 0" 
           class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-12 text-center">
        <div class="w-24 h-24 bg-theme-surface/30 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="w-12 h-12 text-theme-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
          </svg>
        </div>
        <h3 class="text-white-force text-xl font-semibold mb-2">لا توجد طلبات</h3>
        <p class="text-theme-muted mb-6">لم تقم بأي طلبات حتى الآن</p>
        <NuxtLink to="/shop" class="btn-primary px-6 py-3">
          تصفح المتجر
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useSeoMeta({
  title: 'طلباتي - بنتاكون',
  description: 'تتبع جميع طلباتك ومشترياتك في متجر بنتاكون',
})

// Reactive state
const activeFilter = ref('all')

const orderFilters = ref([
  { key: 'all', label: 'جميع الطلبات', count: 8 },
  { key: 'pending', label: 'قيد المعالجة', count: 2 },
  { key: 'processing', label: 'قيد التحضير', count: 1 },
  { key: 'shipped', label: 'تم الشحن', count: 2 },
  { key: 'delivered', label: 'تم التسليم', count: 3 }
])

// Mock orders data
const orders = ref([
  {
    id: '2024001',
    date: 'منذ يومين',
    status: 'delivered',
    total: 350,
    deliveryMethod: 'تسليم فوري',
    trackingNumber: null,
    items: [
      {
        id: 1,
        name: 'بطاقة جينشين إمباكت - 980 جوهرة',
        category: 'بطاقات الألعاب',
        price: 150,
        quantity: 1,
        image: '/products/genshin.jpg',
        downloadable: true
      },
      {
        id: 2,
        name: 'بطاقة ستيم - 200 ريال',
        category: 'بطاقات الألعاب',
        price: 200,
        quantity: 1,
        image: '/products/steam.jpg',
        downloadable: true
      }
    ]
  },
  {
    id: '2024002',
    date: 'منذ أسبوع',
    status: 'shipped',
    total: 450,
    deliveryMethod: 'شحن سريع',
    trackingNumber: 'TR123456789',
    items: [
      {
        id: 3,
        name: 'PlayStation 5 Controller',
        category: 'إكسسوارات',
        price: 450,
        quantity: 1,
        image: '/products/ps5-controller.jpg',
        downloadable: false
      }
    ]
  },
  {
    id: '2024003',
    date: 'منذ أسبوعين',
    status: 'pending',
    total: 100,
    deliveryMethod: 'تسليم فوري',
    trackingNumber: null,
    items: [
      {
        id: 4,
        name: 'بطاقة فورتنايت - V-Bucks',
        category: 'بطاقات الألعاب',
        price: 100,
        quantity: 1,
        image: '/products/fortnite.jpg',
        downloadable: true
      }
    ]
  }
])

// Computed
const filteredOrders = computed(() => {
  if (activeFilter.value === 'all') {
    return orders.value
  }
  return orders.value.filter(order => order.status === activeFilter.value)
})

// Methods
const getStatusClass = (status: string) => {
  switch (status) {
    case 'pending': return 'bg-yellow-500/20 text-yellow-400'
    case 'processing': return 'bg-blue-500/20 text-blue-400'
    case 'shipped': return 'bg-purple-500/20 text-purple-400'
    case 'delivered': return 'bg-green-500/20 text-green-400'
    case 'cancelled': return 'bg-red-500/20 text-red-400'
    default: return 'bg-gray-500/20 text-gray-400'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return 'قيد المعالجة'
    case 'processing': return 'قيد التحضير'
    case 'shipped': return 'تم الشحن'
    case 'delivered': return 'تم التسليم'
    case 'cancelled': return 'ملغي'
    default: return status
  }
}

const cancelOrder = (orderId: string) => {
  console.log('Cancel order:', orderId)
}

const trackOrder = (orderId: string) => {
  console.log('Track order:', orderId)
}
</script>
