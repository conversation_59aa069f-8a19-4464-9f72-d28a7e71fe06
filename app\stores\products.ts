import { defineStore } from 'pinia'
import type { 
  Product, 
  ProductVariant, 
  Category, 
  ProductFilters, 
  ProductStats,
  DigitalCode,
  CustomField
} from '~/types/products'

interface ProductsState {
  products: Product[]
  categories: Category[]
  currentProduct: Product | null
  loading: boolean
  error: string | null
  filters: ProductFilters
  stats: ProductStats | null
}

export const useProductsStore = defineStore('products', {
  state: (): ProductsState => ({
    products: [],
    categories: [],
    currentProduct: null,
    loading: false,
    error: null,
    filters: {
      page: 1,
      limit: 20,
      sort_by: 'created_at',
      sort_order: 'desc'
    },
    stats: null
  }),

  getters: {
    // Get products with applied filters
    filteredProducts: (state) => {
      let filtered = [...state.products]

      if (state.filters.search) {
        const search = state.filters.search.toLowerCase()
        filtered = filtered.filter(product => 
          product.name.toLowerCase().includes(search) ||
          product.description.toLowerCase().includes(search)
        )
      }

      if (state.filters.category) {
        filtered = filtered.filter(product => product.category_id === state.filters.category)
      }

      if (state.filters.status) {
        filtered = filtered.filter(product => product.status === state.filters.status)
      }

      if (state.filters.has_variants !== undefined) {
        filtered = filtered.filter(product => product.has_variants === state.filters.has_variants)
      }

      if (state.filters.min_price || state.filters.max_price) {
        filtered = filtered.filter(product => {
          const minPrice = Math.min(...product.variants.map(v => v.user_price))
          const maxPrice = Math.max(...product.variants.map(v => v.user_price))
          
          if (state.filters.min_price && minPrice < state.filters.min_price) return false
          if (state.filters.max_price && maxPrice > state.filters.max_price) return false
          
          return true
        })
      }

      // Sorting
      if (state.filters.sort_by) {
        filtered.sort((a, b) => {
          let aValue: any, bValue: any

          switch (state.filters.sort_by) {
            case 'name':
              aValue = a.name
              bValue = b.name
              break
            case 'price':
              aValue = Math.min(...a.variants.map(v => v.user_price))
              bValue = Math.min(...b.variants.map(v => v.user_price))
              break

            case 'created_at':
            default:
              aValue = new Date(a.created_at)
              bValue = new Date(b.created_at)
              break
          }

          if (state.filters.sort_order === 'desc') {
            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
          } else {
            return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
          }
        })
      }

      return filtered
    },

    // Get paginated products
    paginatedProducts: (state) => {
      const filtered = state.filteredProducts
      const start = ((state.filters.page || 1) - 1) * (state.filters.limit || 20)
      const end = start + (state.filters.limit || 20)
      return filtered.slice(start, end)
    },

    // Get total pages for pagination
    totalPages: (state) => {
      const total = state.filteredProducts.length
      return Math.ceil(total / (state.filters.limit || 20))
    },

    // Get product by slug
    getProductBySlug: (state) => (slug: string) => {
      return state.products.find(product => product.slug === slug)
    },

    // Get product by ID
    getProductById: (state) => (id: string) => {
      return state.products.find(product => product.id === id)
    },

    // Get category by ID
    getCategoryById: (state) => (id: string) => {
      return state.categories.find(category => category.id === id)
    },

    // Get active categories
    activeCategories: (state) => {
      return state.categories.filter(category => category.is_active)
    },

    // Get featured products
    featuredProducts: (state) => {
      return state.products.filter(product => product.featured && product.status === 'active')
    },

    // Get cheapest variant for a product
    getCheapestVariant: (state) => (productId: string) => {
      const product = state.products.find(p => p.id === productId)
      if (!product || !product.variants.length) return null
      
      return product.variants.reduce((min, variant) => 
        variant.user_price < min.user_price ? variant : min
      )
    },

    // Get product display price (cheapest variant)
    getProductDisplayPrice: (state) => (productId: string) => {
      const cheapest = state.getCheapestVariant(productId)
      return cheapest?.user_price || 0
    }
  },

  actions: {
    // Fetch all products
    async fetchProducts(filters?: ProductFilters) {
      this.loading = true
      this.error = null

      try {
        if (filters) {
          this.setFilters(filters)
        }

        const { useProductsApi } = await import('~/composables/useApi')
        const api = useProductsApi()
        const response = await api.getProducts(this.filters)

        if (response.success) {
          this.products = response.data.products || []
          this.stats = response.data.stats || null
        } else {
          throw new Error(response.error || 'Failed to fetch products')
        }
      } catch (err) {
        this.error = err instanceof Error ? err.message : 'Failed to fetch products'
        console.error('Products fetch error:', err)
      } finally {
        this.loading = false
      }
    },

    // Fetch single product by ID
    async fetchProduct(id: string) {
      this.loading = true
      this.error = null

      try {
        const { useProductsApi } = await import('~/composables/useApi')
        const api = useProductsApi()
        const response = await api.getProduct(id)

        if (response.success) {
          this.currentProduct = response.data.product
          
          // Update product in products array if it exists
          const index = this.products.findIndex(p => p.id === id)
          if (index !== -1) {
            this.products[index] = response.data.product
          }
        } else {
          throw new Error(response.error || 'Failed to fetch product')
        }
      } catch (err) {
        this.error = err instanceof Error ? err.message : 'Failed to fetch product'
        console.error('Product fetch error:', err)
      } finally {
        this.loading = false
      }
    },

    // Fetch categories
    async fetchCategories() {
      try {
        const { useCategoriesApi } = await import('~/composables/useApi')
        const api = useCategoriesApi()
        const response = await api.getCategories()

        if (response.success) {
          this.categories = response.data.categories || []
        } else {
          throw new Error(response.error || 'Failed to fetch categories')
        }
      } catch (err) {
        console.error('Categories fetch error:', err)
      }
    },

    // Set filters
    setFilters(filters: Partial<ProductFilters>) {
      this.filters = { ...this.filters, ...filters }
    },

    // Clear filters
    clearFilters() {
      this.filters = {
        page: 1,
        limit: 20,
        sort_by: 'created_at',
        sort_order: 'desc'
      }
    },

    // Set current product
    setCurrentProduct(product: Product | null) {
      this.currentProduct = product
    },

    // Clear error
    clearError() {
      this.error = null
    }
  }
})
