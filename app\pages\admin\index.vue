<template>
  <div class="space-y-2 lg:space-y-3 xl:space-y-4" dir="rtl">
    <!-- <PERSON> Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-white-force">لوحة الإدارة</h1>
        <p class="text-theme-text-muted">نظرة عامة على أداء المنصة</p>
      </div>
      
      <div class="flex items-center space-x-3 space-x-reverse">
        <button 
          @click="refreshData"
          :disabled="loading"
          class="btn-secondary p-2"
          title="تحديث البيانات"
        >
          <svg 
            class="w-5 h-5 transition-transform"
            :class="{ 'animate-spin': loading }"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-3 lg:gap-4 xl:gap-5">
      <AdminStatCard
        title="إجمالي المستخدمين"
        :value="userStats.totalUsers"
        icon="users"
        icon-color="blue"
        :change="12.5"
        change-label="من الشهر الماضي"
        :loading="loading"
        clickable
        @click="navigateTo('/admin/users')"
      />

      <AdminStatCard
        title="إجمالي الطلبات"
        :value="orderStats.totalOrders"
        icon="orders"
        icon-color="green"
        :change="8.2"
        change-label="من الشهر الماضي"
        :loading="loading"
        clickable
        @click="navigateTo('/admin/orders')"
      />

      <AdminStatCard
        title="إجمالي الإيرادات"
        :value="orderStats.totalRevenue"
        unit="USD"
        icon="revenue"
        icon-color="purple"
        :change="15.3"
        change-label="من الشهر الماضي"
        :loading="loading"
        clickable
        @click="navigateTo('/admin/wallet')"
      />

      <AdminStatCard
        title="الطلبات المعلقة"
        :value="orderStats.pendingOrders"
        icon="pending"
        icon-color="yellow"
        :change="-5.1"
        change-label="من الشهر الماضي"
        :loading="loading"
        clickable
        @click="navigateTo('/admin/orders?status=pending')"
      />
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 xl:grid-cols-10 gap-2 lg:gap-3">
      <NuxtLink to="/admin/users" class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-3 lg:p-4 hover:border-theme-primary/50 transition-all duration-300 group">
        <div class="text-center">
          <div class="w-8 h-8 lg:w-10 lg:h-10 mx-auto mb-2 bg-blue-500/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
            <svg class="w-4 h-4 lg:w-5 lg:h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <div class="text-xs lg:text-sm font-medium text-white-force">المستخدمين</div>
        </div>
      </NuxtLink>

      <NuxtLink to="/admin/products" class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-3 lg:p-4 hover:border-theme-primary/50 transition-all duration-300 group">
        <div class="text-center">
          <div class="w-8 h-8 lg:w-10 lg:h-10 mx-auto mb-2 bg-purple-500/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
            <svg class="w-4 h-4 lg:w-5 lg:h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <div class="text-xs lg:text-sm font-medium text-white-force">المنتجات</div>
        </div>
      </NuxtLink>

      <NuxtLink to="/admin/orders" class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-3 lg:p-4 hover:border-theme-primary/50 transition-all duration-300 group">
        <div class="text-center">
          <div class="w-8 h-8 lg:w-10 lg:h-10 mx-auto mb-2 bg-green-500/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
            <svg class="w-4 h-4 lg:w-5 lg:h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
          </div>
          <div class="text-xs lg:text-sm font-medium text-white-force">الطلبات</div>
        </div>
      </NuxtLink>

      <NuxtLink to="/admin/wallet" class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-3 lg:p-4 hover:border-theme-primary/50 transition-all duration-300 group">
        <div class="text-center">
          <div class="w-8 h-8 lg:w-10 lg:h-10 mx-auto mb-2 bg-yellow-500/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
            <svg class="w-4 h-4 lg:w-5 lg:h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <div class="text-xs lg:text-sm font-medium text-white-force">المحفظة</div>
        </div>
      </NuxtLink>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-3 lg:gap-4 xl:gap-5">
      <!-- Revenue Chart -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl lg:rounded-3xl p-3 lg:p-4 xl:p-5 lg:col-span-2 xl:col-span-3">
        <div class="flex items-center justify-between mb-4 lg:mb-6">
          <h3 class="text-lg font-semibold text-white-force">الإيرادات خلال الأسبوع</h3>
          <div class="text-sm text-theme-text-muted">بالدولار الأمريكي</div>
        </div>
        
        <div class="h-64 flex items-center justify-center">
          <div v-if="loading" class="animate-pulse">
            <div class="h-48 w-full bg-theme-surface-light rounded"></div>
          </div>
          <div v-else class="w-full h-full flex items-end justify-between space-x-2 space-x-reverse">
            <div 
              v-for="(day, index) in revenueData" 
              :key="index"
              class="bg-gradient-to-t from-theme-primary to-theme-secondary rounded-t-lg flex-1 transition-all hover:opacity-80"
              :style="{ height: `${(day.revenue / Math.max(...revenueData.map(d => d.revenue))) * 100}%` }"
              :title="`${day.day}: $${day.revenue}`"
            ></div>
          </div>
        </div>
        
        <div class="flex justify-between text-xs text-theme-text-muted mt-4">
          <span v-for="day in revenueData" :key="day.day">{{ day.day }}</span>
        </div>
      </div>

      <!-- Orders Status Chart -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-white-force">توزيع حالات الطلبات</h3>
        </div>
        
        <div class="h-64 flex items-center justify-center">
          <div v-if="loading" class="animate-pulse">
            <div class="h-32 w-32 bg-theme-surface-light rounded-full"></div>
          </div>
          <div v-else class="relative w-48 h-48">
            <!-- Simple pie chart representation -->
            <div class="w-full h-full rounded-full bg-gradient-to-br from-green-500 to-blue-500 relative overflow-hidden">
              <div class="absolute inset-4 bg-theme-background rounded-full flex items-center justify-center">
                <div class="text-center">
                  <div class="text-2xl font-bold text-white-force">{{ orderStats.totalOrders }}</div>
                  <div class="text-xs text-theme-text-muted">إجمالي الطلبات</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="grid grid-cols-2 gap-4 mt-6">
          <div class="text-center">
            <div class="w-3 h-3 bg-green-500 rounded-full mx-auto mb-1"></div>
            <div class="text-xs text-theme-text-muted">مكتملة</div>
            <div class="text-sm font-medium text-white-force">{{ orderStats.completedOrders }}</div>
          </div>
          <div class="text-center">
            <div class="w-3 h-3 bg-yellow-500 rounded-full mx-auto mb-1"></div>
            <div class="text-xs text-theme-text-muted">معلقة</div>
            <div class="text-sm font-medium text-white-force">{{ orderStats.pendingOrders }}</div>
          </div>
          <div class="text-center">
            <div class="w-3 h-3 bg-blue-500 rounded-full mx-auto mb-1"></div>
            <div class="text-xs text-theme-text-muted">قيد المعالجة</div>
            <div class="text-sm font-medium text-white-force">{{ orderStats.processingOrders }}</div>
          </div>
          <div class="text-center">
            <div class="w-3 h-3 bg-red-500 rounded-full mx-auto mb-1"></div>
            <div class="text-xs text-theme-text-muted">مرفوضة</div>
            <div class="text-sm font-medium text-white-force">{{ orderStats.totalOrders - orderStats.completedOrders - orderStats.pendingOrders - orderStats.processingOrders }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-4 lg:p-5 xl:p-6">
      <div class="flex items-center justify-between mb-4 lg:mb-6">
        <h3 class="text-lg font-semibold text-white-force">النشاط الأخير</h3>
        <NuxtLink to="/admin/orders" class="text-sm text-theme-primary hover:text-theme-primary-hover">
          عرض الكل
        </NuxtLink>
      </div>
      
      <div v-if="loading" class="space-y-4">
        <div v-for="i in 5" :key="i" class="animate-pulse flex items-center space-x-4 space-x-reverse">
          <div class="w-10 h-10 bg-theme-surface-light rounded-full"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-theme-surface-light rounded w-3/4"></div>
            <div class="h-3 bg-theme-surface-light rounded w-1/2"></div>
          </div>
        </div>
      </div>
      
      <div v-else class="space-y-4">
        <div 
          v-for="activity in recentActivity" 
          :key="activity.id"
          class="flex items-center space-x-4 space-x-reverse p-3 rounded-lg hover:bg-theme-surface/30 transition-colors"
        >
          <div 
            class="w-10 h-10 rounded-full flex items-center justify-center"
            :class="getActivityIconClass(activity.type)"
          >
            <component :is="getActivityIcon(activity.type)" class="w-5 h-5" />
          </div>
          
          <div class="flex-1 min-w-0">
            <p class="text-sm text-white-force">{{ activity.description }}</p>
            <div class="flex items-center space-x-2 space-x-reverse text-xs text-theme-text-muted">
              <span>{{ formatRelativeTime(activity.createdAt) }}</span>
              <span v-if="activity.amount" class="text-theme-primary">
                ${{ activity.amount }} USD
                <span v-if="activity.originalCurrency && activity.originalCurrency !== 'USD'">
                  ({{ activity.originalAmount }} {{ activity.originalCurrency }})
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  ShoppingBagIcon, 
  CreditCardIcon, 
  UserIcon,
  CurrencyDollarIcon
} from '@heroicons/vue/24/outline'

// Layout
definePageMeta({
  layout: 'admin'
  // middleware: ['auth', 'admin'] // Disabled for development
})

// Composables
const { stats: userStats } = useAdminUsers()
const { stats: orderStats, getRevenueByPeriod } = useAdminOrders()
const { stats: walletStats } = useAdminWallet()

// State
const loading = ref(false)

// Revenue data for chart (last 7 days)
const revenueData = computed(() => {
  const days = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة']
  return days.map(day => ({
    day,
    revenue: Math.floor(Math.random() * 1000) + 200 // Mock data
  }))
})

// Recent activity (merged orders and wallet transactions)
const recentActivity = computed(() => {
  // Mock recent activity data
  return [
    {
      id: 1,
      type: 'order',
      description: 'طلب جديد من أحمد محمد - بطاقة ستيم 50 دولار',
      createdAt: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
      amount: 50.00,
      originalAmount: 187.50,
      originalCurrency: 'SAR'
    },
    {
      id: 2,
      type: 'wallet',
      description: 'إيداع في محفظة فاطمة علي',
      createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      amount: 100.00,
      originalAmount: 100.00,
      originalCurrency: 'USD'
    },
    {
      id: 3,
      type: 'order',
      description: 'تم إكمال طلب محمد حسن - بطاقة جوجل بلاي',
      createdAt: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
      amount: 25.00,
      originalAmount: 91.75,
      originalCurrency: 'AED'
    },
    {
      id: 4,
      type: 'user',
      description: 'مستخدم جديد: خالد عبدالله',
      createdAt: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
      amount: null
    },
    {
      id: 5,
      type: 'wallet',
      description: 'طلب سحب من عائشة أحمد',
      createdAt: new Date(Date.now() - 1000 * 60 * 90).toISOString(), // 1.5 hours ago
      amount: 50.00,
      originalAmount: 187.50,
      originalCurrency: 'SAR'
    }
  ]
})

// Methods
const refreshData = async () => {
  loading.value = true
  try {
    // Simulate API refresh
    await new Promise(resolve => setTimeout(resolve, 1000))
  } finally {
    loading.value = false
  }
}

const getActivityIcon = (type: string) => {
  const icons = {
    order: ShoppingBagIcon,
    wallet: CreditCardIcon,
    user: UserIcon,
    currency: CurrencyDollarIcon
  }
  return icons[type as keyof typeof icons] || ShoppingBagIcon
}

const getActivityIconClass = (type: string) => {
  const classes = {
    order: 'bg-blue-500/20 text-blue-400',
    wallet: 'bg-green-500/20 text-green-400',
    user: 'bg-purple-500/20 text-purple-400',
    currency: 'bg-yellow-500/20 text-yellow-400'
  }
  return classes[type as keyof typeof classes] || 'bg-gray-500/20 text-gray-400'
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'الآن'
  if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `منذ ${diffInHours} ساعة`
  
  const diffInDays = Math.floor(diffInHours / 24)
  return `منذ ${diffInDays} يوم`
}

// Auto-refresh every 30 seconds
onMounted(() => {
  const interval = setInterval(refreshData, 30000)
  onUnmounted(() => clearInterval(interval))
})
</script>
