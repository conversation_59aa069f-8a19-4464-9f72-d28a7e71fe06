<template>
  <div class="space-y-4 sm:space-y-6" dir="rtl">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold text-white-force">إدارة الطلبات</h1>
        <p class="text-sm sm:text-base text-theme-text-muted">إدارة الطلبات وتعيين العمال وتتبع الحالات</p>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-5 xl:gap-6">
      <AdminStatCard
        title="إجمالي الطلبات"
        :value="stats.totalOrders"
        icon="orders"
        icon-color="blue"
        :loading="loading"
      />
      
      <AdminStatCard
        title="الطلبات المكتملة"
        :value="stats.completedOrders"
        icon="orders"
        icon-color="green"
        :loading="loading"
      />
      
      <AdminStatCard
        title="الطلبات المعلقة"
        :value="stats.pendingOrders"
        icon="pending"
        icon-color="yellow"
        :loading="loading"
      />
      
      <AdminStatCard
        title="إجمالي الإيرادات"
        :value="stats.totalRevenue"
        unit="USD"
        icon="revenue"
        icon-color="purple"
        :loading="loading"
      />
    </div>

    <!-- Orders Table -->
    <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl sm:rounded-3xl p-4 sm:p-6 shadow-xl">
      <h3 class="text-lg sm:text-xl font-semibold text-white-force mb-4 sm:mb-6">قائمة الطلبات</h3>

      <!-- Filters -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 sm:gap-4 mb-4 sm:mb-6">
        <!-- Search -->
        <div>
          <input
            v-model="filters.search"
            type="text"
            placeholder="البحث برقم الطلب أو اسم العميل"
            class="w-full px-3 py-2 text-sm sm:text-base bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary transition-all duration-200"
          />
        </div>
        
        <!-- Status Filter -->
        <div>
          <select
            v-model="filters.status"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          >
            <option value="">جميع الحالات</option>
            <option value="pending">في الانتظار</option>
            <option value="processing">قيد المعالجة</option>
            <option value="completed">مكتمل</option>
            <option value="declined">مرفوض</option>
          </select>
        </div>
        
        <!-- Date From -->
        <div>
          <input
            v-model="filters.dateFrom"
            type="date"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          />
        </div>
        
        <!-- Date To -->
        <div>
          <input
            v-model="filters.dateTo"
            type="date"
            class="w-full px-3 py-2 bg-theme-surface border border-theme-light rounded-lg text-white-force focus:outline-none focus:ring-2 focus:ring-theme-primary"
          />
        </div>
        
        <!-- Clear Filters -->
        <div>
          <button
            @click="clearFilters"
            class="w-full btn-secondary py-2"
          >
            مسح الفلاتر
          </button>
        </div>
      </div>

      <!-- Orders List -->
      <div class="space-y-4">
        <div 
          v-for="order in mockOrders" 
          :key="order.id"
          class="bg-theme-surface/30 rounded-2xl p-4 hover:bg-theme-surface/50 transition-colors"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-4 space-x-reverse">
              <div>
                <h4 class="font-medium text-white-force">{{ order.id }}</h4>
                <p class="text-sm text-theme-text-muted">{{ order.productName }}</p>
              </div>
              <div class="text-right">
                <p class="text-sm text-theme-text-muted">{{ order.userName }}</p>
                <p class="text-xs text-theme-text-muted">{{ order.userEmail }}</p>
              </div>
            </div>
            <span 
              class="px-3 py-1 rounded-full text-xs font-medium"
              :class="getStatusClass(order.status)"
            >
              {{ getStatusText(order.status) }}
            </span>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
            <div>
              <span class="text-theme-text-muted">المبلغ المدفوع:</span>
              <div class="text-white-force font-medium">
                {{ order.paidAmount }} {{ order.paidCurrency }}
              </div>
              <div class="text-xs text-theme-primary">
                ≈ ${{ order.usdEquivalent }} USD
              </div>
            </div>
            <div>
              <span class="text-theme-text-muted">سعر الصرف:</span>
              <div class="text-white-force font-medium">{{ order.exchangeRate }}</div>
            </div>
            <div>
              <span class="text-theme-text-muted">العامل المعين:</span>
              <div class="text-white-force font-medium">
                {{ order.assignedWorker || 'غير معين' }}
              </div>
            </div>
            <div>
              <span class="text-theme-text-muted">تاريخ الطلب:</span>
              <div class="text-white-force font-medium">{{ formatDate(order.createdAt) }}</div>
            </div>
          </div>
          
          <div v-if="order.notes" class="mb-4">
            <span class="text-theme-text-muted text-sm">ملاحظات:</span>
            <p class="text-theme-text-secondary text-sm mt-1">{{ order.notes }}</p>
          </div>
          
          <div v-if="order.deliveryCode" class="mb-4 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <span class="text-green-400 text-sm font-medium">كود التسليم:</span>
            <p class="text-white-force font-mono text-lg mt-1">{{ order.deliveryCode }}</p>
          </div>
          
          <div class="flex justify-end space-x-2 space-x-reverse">
            <button
              @click="viewOrder(order)"
              class="btn-secondary text-xs px-3 py-1"
            >
              عرض التفاصيل
            </button>
            <button
              v-if="order.status === 'pending'"
              @click="assignWorker(order)"
              class="btn-primary text-xs px-3 py-1"
            >
              تعيين عامل
            </button>
            <button
              v-if="order.status === 'processing'"
              @click="completeOrder(order)"
              class="btn-primary text-xs px-3 py-1"
            >
              إكمال الطلب
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'admin'
})

// State
const loading = ref(false)
const filters = ref({
  search: '',
  status: '',
  dateFrom: '',
  dateTo: ''
})

// Mock data
const mockOrders = ref([
  {
    id: 'ORD-001',
    productName: 'بطاقة ستيم 50 دولار',
    userName: 'أحمد محمد',
    userEmail: '<EMAIL>',
    paidAmount: 187.50,
    paidCurrency: 'SAR',
    usdEquivalent: 50.00,
    exchangeRate: 3.75,
    status: 'pending',
    assignedWorker: null,
    createdAt: '2024-01-15T10:30:00Z',
    notes: 'طلب عادي'
  },
  {
    id: 'ORD-002',
    productName: 'حساب نتفليكس مشترك',
    userName: 'فاطمة علي',
    userEmail: '<EMAIL>',
    paidAmount: 59.97,
    paidCurrency: 'SAR',
    usdEquivalent: 15.99,
    exchangeRate: 3.75,
    status: 'processing',
    assignedWorker: 'محمد أحمد',
    createdAt: '2024-01-15T09:15:00Z',
    deliveryCode: 'NFLX-ABC123'
  },
  {
    id: 'ORD-003',
    productName: 'بطاقة جوجل بلاي 25 دولار',
    userName: 'خالد سعد',
    userEmail: '<EMAIL>',
    paidAmount: 93.75,
    paidCurrency: 'SAR',
    usdEquivalent: 25.00,
    exchangeRate: 3.75,
    status: 'completed',
    assignedWorker: 'سارة محمد',
    createdAt: '2024-01-14T16:45:00Z',
    deliveryCode: 'GPLY-XYZ789'
  }
])

const stats = computed(() => ({
  totalOrders: mockOrders.value.length,
  completedOrders: mockOrders.value.filter(o => o.status === 'completed').length,
  pendingOrders: mockOrders.value.filter(o => o.status === 'pending').length,
  totalRevenue: mockOrders.value.reduce((sum, o) => sum + o.usdEquivalent, 0)
}))

// Methods
const viewOrder = (order: any) => {
  console.log('View order:', order)
}

const assignWorker = (order: any) => {
  order.status = 'processing'
  order.assignedWorker = 'عامل متاح'
}

const completeOrder = (order: any) => {
  order.status = 'completed'
  order.deliveryCode = 'CODE-' + Math.random().toString(36).substr(2, 6).toUpperCase()
}

const clearFilters = () => {
  filters.value = {
    search: '',
    status: '',
    dateFrom: '',
    dateTo: ''
  }
}

// Helper functions
const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30',
    processing: 'bg-blue-500/20 text-blue-400 border border-blue-500/30',
    completed: 'bg-green-500/20 text-green-400 border border-green-500/30',
    declined: 'bg-red-500/20 text-red-400 border border-red-500/30'
  }
  return classes[status as keyof typeof classes] || classes.pending
}

const getStatusText = (status: string) => {
  const texts = {
    pending: 'في الانتظار',
    processing: 'قيد المعالجة',
    completed: 'مكتمل',
    declined: 'مرفوض'
  }
  return texts[status as keyof typeof texts] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
