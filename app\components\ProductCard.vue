<template>
  <NuxtLink :to="`/product/${product.slug}`">
    <div class="product-card h-full flex flex-col group">
      <!-- Product Image - Square aspect ratio (1:1) - 114.67 x 114.67 -->
      <div class="relative aspect-square overflow-hidden">
        <img
          :src="product.main_image || '/logo.jpg'"
          :alt="product.name"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />

        <!-- Badges -->
        <div class="absolute top-1 right-1 flex flex-col gap-1">
          <div
            v-for="badge in productBadges"
            :key="badge.type"
            :class="getBadgeClass(badge.type)"
            class="badge text-xs px-2 py-1 rounded"
          >
            {{ badge.text }}
          </div>
        </div>
      </div>

      <!-- Product Info - 114.67 x 71 -->
      <div class="p-2 flex flex-col justify-between bg-theme-surface" style="height: 71px;">
        <!-- Product Title -->
        <h3 class="text-xs font-semibold product-title mb-1 line-clamp-2 leading-tight">
          {{ product.name }}
        </h3>

        <!-- Bottom Section: Rating + Price -->
        <div class="flex items-center justify-between">
          <!-- Rating -->
          <div class="flex items-center space-x-1 space-x-reverse" v-if="product.rating">
            <span class="badge-rating font-bold text-xs">{{ product.rating }}</span>
            <div class="flex">
              <svg
                v-for="i in 5"
                :key="i"
                class="w-2 h-2 badge-rating fill-current"
                viewBox="0 0 24 24"
              >
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
          </div>

          <!-- Price Section -->
          <div class="text-left">
            <div class="product-price text-xs">
              ${{ displayPrice.toFixed(2) }}
            </div>
            <div
              v-if="originalPrice && originalPrice > displayPrice"
              class="text-theme-muted text-xs line-through -mt-0.5"
            >
              ${{ originalPrice.toFixed(2) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </NuxtLink>
</template>

<script setup lang="ts">
import type { Product } from '~/types/products'

interface Props {
  product: Product
}

const props = defineProps<Props>()

// Get the cheapest variant for display
const cheapestVariant = computed(() => {
  if (!props.product.variants || props.product.variants.length === 0) return null
  return props.product.variants.reduce(
    (min, variant) => (variant.user_price < min.user_price ? variant : min),
    props.product.variants[0]
  )
})

// Display price (use discount price if available, otherwise user price)
const displayPrice = computed(() => {
  const variant = cheapestVariant.value
  if (!variant) return 0
  return variant.discount_price && variant.discount_price < variant.user_price
    ? variant.discount_price
    : variant.user_price
})

// Original price for strikethrough
const originalPrice = computed(() => {
  const variant = cheapestVariant.value
  if (!variant) return 0
  return variant.discount_price && variant.discount_price < variant.user_price
    ? variant.user_price
    : null
})

// Product badges
const productBadges = computed(() => {
  const badges = []

  if (props.product.featured) {
    badges.push({ type: 'featured', text: 'مميز' })
  }

  // Check for discount
  const variant = cheapestVariant.value
  if (variant?.discount_price && variant.discount_price < variant.user_price) {
    const discount = Math.round(((variant.user_price - variant.discount_price) / variant.user_price) * 100)
    badges.push({ type: 'discount', text: `-${discount}%` })
  }

  return badges
})

// Badge styling
const getBadgeClass = (type: string) => {
  switch (type) {
    case 'featured':
      return 'badge-featured bg-theme-primary text-white'
    case 'discount':
      return 'badge-discount bg-red-500 text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
